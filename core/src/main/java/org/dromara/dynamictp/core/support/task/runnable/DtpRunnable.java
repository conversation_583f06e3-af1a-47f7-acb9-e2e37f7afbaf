/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.core.support.task.runnable;

import lombok.Getter;
import org.slf4j.MDC;

import static org.dromara.dynamictp.common.constant.DynamicTpConst.TRACE_ID;

/**
 * DtpRunnable related
 *
 * <AUTHOR>
 * @since 1.0.4
 */
@Getter
public class DtpRunnable implements Runnable {

    private final Runnable originRunnable;

    private final Runnable runnable;

    private final String taskName;

    private final String traceId;

    public DtpRunnable(Runnable originRunnable, Runnable runnable, String taskName) {
        this.originRunnable = originRunnable;
        this.runnable = runnable;
        this.taskName = taskName;
        this.traceId = MDC.get(TRACE_ID);
    }

    @Override
    public void run() {
        runnable.run();
    }

}
