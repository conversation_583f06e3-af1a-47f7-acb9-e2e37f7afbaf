/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.core.support.init;

import org.apache.commons.collections4.CollectionUtils;
import org.dromara.dynamictp.common.util.ExtensionServiceLoader;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * DtpInitializerExecutor related
 *
 * <AUTHOR>
 * @since 1.1.6
 */
public class DtpInitializerExecutor {

    private static final AtomicBoolean INITIALIZED = new AtomicBoolean(false);

    public static void init(Object... args) {
        if (!INITIALIZED.compareAndSet(false, true)) {
            return;
        }
        List<DtpInitializer> loadedInitializers = ExtensionServiceLoader.get(DtpInitializer.class);
        if (CollectionUtils.isEmpty(loadedInitializers)) {
            return;
        }
        loadedInitializers.sort(Comparator.comparingInt(DtpInitializer::getOrder));
        loadedInitializers.forEach(i -> i.init(args));
    }
}
