/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.core.system;

import lombok.extern.slf4j.Slf4j;

import java.lang.management.ManagementFactory;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.RuntimeMXBean;
import java.util.concurrent.TimeUnit;

/**
 * CpuMetricsCaptor related
 *
 * <AUTHOR>
 * @since 1.1.6
 */
@Slf4j
public class CpuMetricsCaptor implements Runnable {

    private double currProcessCpuUsage = -1;

    private long prevProcessCpuTime = 0;

    private long prevUpTime = 0;

    public double getProcessCpuUsage() {
        return currProcessCpuUsage;
    }

    @Override
    public void run() {
        try {
            OperatingSystemMXBean osBean = ManagementFactory.getPlatformMXBean(OperatingSystemMXBean.class);
            int cpuCores = osBean.getAvailableProcessors();

            long newProcessCpuTime = OperatingSystemBeanManager.getProcessCpuTime();
            RuntimeMXBean runtimeBean = ManagementFactory.getPlatformMXBean(RuntimeMXBean.class);
            long newUpTime = runtimeBean.getUptime();
            long elapsedCpu = TimeUnit.NANOSECONDS.toMillis(newProcessCpuTime - prevProcessCpuTime);
            long elapsedTime = newUpTime - prevUpTime;
            double processCpuUsage = (double) elapsedCpu / elapsedTime / cpuCores;
            prevProcessCpuTime = newProcessCpuTime;
            prevUpTime = newUpTime;
            currProcessCpuUsage = processCpuUsage;
        } catch (Throwable e) {
            log.error("Get system metrics error.", e);
        }
    }
}
