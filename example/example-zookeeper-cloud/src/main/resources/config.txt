/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.enabled=true
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.enabledBanner=true
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.enabledCollect=true
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.collectorType=logging
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.monitorInterval=5
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].threadPoolName=dtpExecutor1
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].corePoolSize=50
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].maximumPoolSize=50
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].queueCapacity=3000
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].queueType=VariableLinkedBlockingQueue
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].rejectedHandlerType=CallerRunsPolicy
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].keepAliveTime=50
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].allowCoreThreadTimeOut=true
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].threadNamePrefix=test1
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].notifyItems[0].type=capacity
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].notifyItems[0].enabled=true
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].notifyItems[0].threshold=80
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].notifyItems[0].platforms[0]=ding
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].notifyItems[0].platforms[1]=wechat
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].notifyItems[0].interval=120
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].notifyItems[1].type=change
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].notifyItems[1].enabled=true
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].notifyItems[2].type=liveness
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].notifyItems[2].enabled=true
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].notifyItems[2].threshold=80
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].notifyItems[3].type=reject
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].notifyItems[3].enabled=true
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[0].notifyItems[3].threshold=1
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].threadPoolName=dtpExecutor2
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].corePoolSize=20
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].maximumPoolSize=30
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].queueCapacity=1000
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].queueType=VariableLinkedBlockingQueue
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].rejectedHandlerType=CallerRunsPolicy
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].keepAliveTime=50
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].allowCoreThreadTimeOut=true
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].threadNamePrefix=test2
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].notifyItems[0].type=capacity
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].notifyItems[0].enabled=true
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].notifyItems[0].threshold=80
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].notifyItems[0].platforms[0]=ding
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].notifyItems[0].platforms[1]=wechat
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].notifyItems[0].interval=120
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].notifyItems[1].type=change
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].notifyItems[1].enabled=true
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].notifyItems[2].type=liveness
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].notifyItems[2].enabled=true
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].notifyItems[2].threshold=80
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].notifyItems[3].type=reject
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].notifyItems[3].enabled=true
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.executors[1].notifyItems[3].threshold=1

/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.platforms[0].platform=wechat
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.platforms[0].urlKey=38a7e53d8b649c
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.platforms[0].receivers=test
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.platforms[1].platform=ding
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.platforms[1].urlKey=f80dad44d4a8801d593604f4a08dcd6a
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.platforms[1].secret=SECb5444f2c8346741fa6f375d5b9d21
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.platforms[1].receivers=18888888888

/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.dubboTp[0].threadPoolName=dubboTp#20880
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.dubboTp[0].corePoolSize=100
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.dubboTp[0].maximumPoolSize=400
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.dubboTp[0].keepAliveTime=40

/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.rocketMqTp[0].threadPoolName=test#test
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.rocketMqTp[0].corePoolSize=100
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.rocketMqTp[0].maximumPoolSize=400
/configserver/dev/dynamic-tp-cloud-zookeeper-demo,dev=dynamictp.rocketMqTp[0].keepAliveTime=40