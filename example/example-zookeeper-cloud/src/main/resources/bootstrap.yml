server:
  port: 8890

spring:
  application:
    name: dynamic-tp-cloud-zookeeper-demo
  cloud:
    zookeeper:
      connect-string: 127.0.0.1:2181
      config:
        root: /configserver/dev
      enabled: true
  profiles:
    active: dev

# 开启 SpringBoot Actuator Endpoint 暴露出DynamicTp指标接口
# 开启 prometheus 指标采集端点
management:
  metrics:
    export:
      prometheus:
        enabled: true
  endpoints:
    web:
      exposure:
        include: '*'   # 测试使用，线上不要用*，按需开启