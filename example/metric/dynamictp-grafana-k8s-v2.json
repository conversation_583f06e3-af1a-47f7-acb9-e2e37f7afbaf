{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "线程池监控", "editable": true, "gnetId": null, "graphTooltip": 0, "id": 70, "iteration": 1653643074175, "links": [], "panels": [{"datasource": null, "description": "当前线程数", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "hue", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 12, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "thread_pool_current_size{cluster=\"$cluster\",namespace=\"$namespace\",container=\"$container\",pod=\"$pod\",thread_pool_name=~\"$threadpool\"}", "interval": "", "legendFormat": "{{thread_pool_name}}", "refId": "A"}], "title": "当前线程数", "transparent": true, "type": "timeseries"}, {"datasource": null, "description": "活跃线程数", "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 8, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "thread_pool_active_count{cluster=\"$cluster\",namespace=\"$namespace\",container=\"$container\",pod=\"$pod\",thread_pool_name=~\"$threadpool\"}", "interval": "", "legendFormat": "{{thread_pool_name}}", "refId": "A"}], "title": "活跃线程数", "type": "timeseries"}, {"datasource": null, "description": "任务队列大小", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 6, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "thread_pool_queue_size{cluster=\"$cluster\",namespace=\"$namespace\",container=\"$container\",pod=\"$pod\",thread_pool_name=~\"$threadpool\"}\n", "interval": "", "legendFormat": "{{thread_pool_name}}", "refId": "A"}], "title": "任务队列大小", "type": "timeseries"}, {"datasource": null, "description": "任务队列剩余大小", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "orange", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 2, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "thread_pool_queue_remaining_capacity{cluster=\"$cluster\",namespace=\"$namespace\",container=\"$container\",pod=\"$pod\",thread_pool_name=~\"$threadpool\"}", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{thread_pool_name}}", "refId": "B"}], "title": "任务队列剩余大小", "type": "timeseries"}, {"datasource": null, "description": "完成任务数", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 10, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "thread_pool_completed_task_count{cluster=\"$cluster\",namespace=\"$namespace\",container=\"$container\",pod=\"$pod\",thread_pool_name=~\"$threadpool\"}", "interval": "", "legendFormat": "{{thread_pool_name}}", "refId": "A"}], "title": "完成任务数", "type": "timeseries"}, {"datasource": null, "description": "拒绝任务数", "fieldConfig": {"defaults": {"color": {"mode": "continuous-reds"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 4, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "thread_pool_reject_count{cluster=\"$cluster\",namespace=\"$namespace\",container=\"$container\",pod=\"$pod\",thread_pool_name=~\"$threadpool\"}\n", "format": "time_series", "hide": false, "interval": "", "legendFormat": "{{thread_pool_name}}", "refId": "B"}], "title": "拒绝任务数", "type": "timeseries"}, {"datasource": null, "description": "执行超时任务数", "fieldConfig": {"defaults": {"color": {"mode": "continuous-reds"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 14, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "thread_pool_run_timeout_count{cluster=\"$cluster\",namespace=\"$namespace\",container=\"$container\",pod=\"$pod\",thread_pool_name=~\"$threadpool\"}", "interval": "", "legendFormat": "{{thread_pool_name}}", "refId": "A"}], "title": "执行超时任务数", "type": "timeseries"}, {"datasource": null, "description": "队列等待超时任务数", "fieldConfig": {"defaults": {"color": {"mode": "continuous-reds"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "id": 16, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "thread_pool_queue_timeout_count{cluster=\"$cluster\",namespace=\"$namespace\",container=\"$container\",pod=\"$pod\",thread_pool_name=~\"$threadpool\"}", "interval": "", "legendFormat": "{{thread_pool_name}}", "refId": "A"}], "title": "队列等待超时任务数", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true}, "datasource": {"type": "prometheus"}, "definition": "label_values(cluster)", "hide": 0, "includeAll": false, "label": "cluster", "multi": false, "name": "cluster", "options": [], "query": {"query": "label_values(cluster)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "(k8s-hw-bj-1-prod|k8s-tc-bj-1-dev|k8s-tc-bj-1-test)", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false}, "datasource": {"type": "prometheus"}, "definition": "label_values(kube_namespace_created{job=\"kube-state-metrics\", cluster=\"$cluster\"}, namespace)", "hide": 0, "includeAll": false, "label": "namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(kube_namespace_created{job=\"kube-state-metrics\", cluster=\"$cluster\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": false}, "datasource": {"type": "prometheus"}, "definition": "label_values(kube_pod_container_info{cluster=~\"$cluster\",namespace=~\"$namespace\"},container)", "hide": 0, "includeAll": false, "label": "container", "multi": false, "name": "container", "options": [], "query": {"query": "label_values(kube_pod_container_info{cluster=~\"$cluster\",namespace=~\"$namespace\"},container)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false}, "datasource": {"type": "prometheus"}, "definition": "label_values(kube_pod_container_info{cluster=~\"$cluster\",namespace=~\"$namespace\",container=~\"$container\"},pod)", "hide": 0, "includeAll": false, "label": "pod", "multi": false, "name": "pod", "options": [], "query": {"query": "label_values(kube_pod_container_info{cluster=~\"$cluster\",namespace=~\"$namespace\",container=~\"$container\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": "", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": null, "definition": "label_values(thread_pool_current_size{app_kubernetes_io_instance=\"$container\", pod=~\"$pod\"},thread_pool_name)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "ThreadPoolName", "multi": false, "name": "threadpool", "options": [], "query": {"query": "label_values(thread_pool_current_size{app_kubernetes_io_instance=\"$container\", kubernetes_pod_name=~\"$pod\"},thread_pool_name)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "线程池监控（DynamicTp）", "version": 8}