{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 1, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus"}, "description": "核心线程数", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": [], "unit": "none"}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 0}, "id": 25, "options": {"legend": {"displayMode": "table", "placement": "bottom", "showLegend": true, "values": ["value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.0.1", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "thread_pool_core_size{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "instant": false, "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "核心线程数", "type": "piechart"}, {"datasource": {"type": "prometheus"}, "description": "最大线程数", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 0}, "id": 26, "options": {"legend": {"displayMode": "table", "placement": "bottom", "showLegend": true, "values": ["value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.0.1", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "thread_pool_maximum_size{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "instant": false, "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "最大线程数", "type": "piechart"}, {"datasource": {"type": "prometheus"}, "description": "tps", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 0}, "id": 28, "options": {"legend": {"displayMode": "table", "placement": "bottom", "showLegend": true, "values": ["value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.0.1", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "thread_pool_tps{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "instant": false, "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "tps", "type": "piechart"}, {"datasource": {"type": "prometheus"}, "description": "tp99 耗时", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 0}, "id": 29, "options": {"displayLabels": [], "legend": {"displayMode": "table", "placement": "bottom", "showLegend": true, "values": ["value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.0.1", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "thread_pool_completed_task_time_tp99{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "instant": false, "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "tp99 耗时", "type": "piechart"}, {"datasource": {"type": "prometheus"}, "description": "当前线程数", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 5}, "id": 27, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_current_size{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "当前线程数", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "活跃线程数", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 5}, "id": 8, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_active_count{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "活跃线程数", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "任务队列大小", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 13}, "id": 6, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_queue_size{app_name=\"$application\", thread_pool_name=\"$threadpool\"}\n", "interval": "", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "任务队列大小", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "任务队列剩余大小", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "orange", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 13}, "id": 2, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_queue_remaining_capacity{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "range": true, "refId": "B"}], "title": "任务队列剩余大小", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "完成任务数", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 21}, "id": 10, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_completed_task_count{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "完成任务数", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "拒绝任务数", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 21}, "id": 4, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_reject_count{app_name=\"$application\", thread_pool_name=\"$threadpool\"}\n", "format": "time_series", "hide": false, "interval": "", "legendFormat": "{{instance}}", "range": true, "refId": "B"}], "title": "拒绝任务数", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "执行超时任务数", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 29}, "id": 14, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_run_timeout_count{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "执行超时任务数", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "排队超时任务数", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 29}, "id": 18, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_queue_timeout_count{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "排队超时任务数", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "tps", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 37}, "id": 17, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_tps{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "tps", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "tp999 耗时", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 37}, "id": 16, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_completed_task_time_tp999{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "tp999 耗时", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "tp99 耗时", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 45}, "id": 19, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_completed_task_time_tp99{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "tp99 耗时", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "tp95 耗时", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 45}, "id": 20, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_completed_task_time_tp95{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "tp95 耗时", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "tp90 耗时", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 53}, "id": 21, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_completed_task_time_tp90{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "tp90 耗时", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "tp50 耗时", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 53}, "id": 22, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_completed_task_time_tp50{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "tp50 耗时", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "任务平均耗时", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 61}, "id": 23, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_completed_task_time_avg{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "任务平均耗时", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "任务最大耗时", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 61}, "id": 24, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_completed_task_time_max{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "title": "任务最大耗时", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false}, "datasource": {"type": "prometheus"}, "definition": "label_values(app_name)", "hide": 0, "includeAll": false, "label": "application", "multi": false, "name": "application", "options": [], "query": {"query": "label_values(app_name)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false}, "datasource": {"type": "prometheus"}, "definition": "label_values({app_name=\"$application\"}, thread_pool_name)", "hide": 0, "includeAll": false, "label": "threadpool", "multi": false, "name": "threadpool", "options": [], "query": {"query": "label_values({app_name=\"$application\"}, thread_pool_name)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "线程池监控（DynamicTp）", "version": 32, "weekStart": ""}