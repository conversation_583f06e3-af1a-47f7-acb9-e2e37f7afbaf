{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 91, "iteration": 1697815562043, "links": [], "panels": [{"aliasColors": {}, "breakPoint": "50%", "cacheTimeout": null, "combine": {"label": "Others", "threshold": 0}, "datasource": null, "description": "核心线程数", "fieldConfig": {"defaults": {}, "overrides": []}, "fontSize": "80%", "format": "short", "gridPos": {"h": 5, "w": 6, "x": 0, "y": 0}, "id": 25, "interval": null, "legend": {"show": true, "values": true}, "legendType": "Under graph", "links": [], "nullPointMode": "connected", "pieType": "pie", "pluginVersion": "7.5.17", "strokeWidth": 1, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_core_size{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "instant": false, "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "range": true, "refId": "A"}], "title": "核心线程数", "type": "grafana-piechart-panel", "valueName": "current"}, {"aliasColors": {}, "breakPoint": "50%", "cacheTimeout": null, "combine": {"label": "Others", "threshold": 0}, "datasource": null, "description": "最大线程数", "fieldConfig": {"defaults": {}, "overrides": []}, "fontSize": "80%", "format": "short", "gridPos": {"h": 5, "w": 6, "x": 6, "y": 0}, "id": 26, "interval": null, "legend": {"show": true, "values": true}, "legendType": "Under graph", "links": [], "nullPointMode": "connected", "pieType": "pie", "pluginVersion": "7.5.17", "strokeWidth": 1, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_maximum_size{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "range": true, "refId": "A"}], "title": "最大线程数", "type": "grafana-piechart-panel", "valueName": "current"}, {"aliasColors": {}, "breakPoint": "50%", "cacheTimeout": null, "combine": {"label": "Others", "threshold": 0}, "datasource": null, "description": "tps", "fieldConfig": {"defaults": {}, "overrides": []}, "fontSize": "80%", "format": "short", "gridPos": {"h": 5, "w": 6, "x": 12, "y": 0}, "id": 28, "interval": null, "legend": {"show": true, "values": true}, "legendType": "Under graph", "links": [], "nullPointMode": "connected", "pieType": "pie", "pluginVersion": "7.5.17", "strokeWidth": 1, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_tps{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "instant": false, "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "range": true, "refId": "A"}], "title": "tps", "type": "grafana-piechart-panel", "valueName": "current"}, {"aliasColors": {}, "breakPoint": "50%", "cacheTimeout": null, "combine": {"label": "Others", "threshold": 0}, "datasource": null, "description": "tp99 耗时", "fieldConfig": {"defaults": {}, "overrides": []}, "fontSize": "80%", "format": "short", "gridPos": {"h": 5, "w": 6, "x": 18, "y": 0}, "id": 29, "interval": null, "legend": {"show": true, "values": true}, "legendType": "Under graph", "links": [], "nullPointMode": "connected", "pieType": "pie", "pluginVersion": "7.5.17", "strokeWidth": 1, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_completed_task_time_tp99{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "instant": false, "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "range": true, "refId": "A"}], "title": "tp99 耗时", "type": "grafana-piechart-panel", "valueName": "current"}, {"datasource": null, "description": "当前线程数", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 5}, "id": 27, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["min", "max", "last"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_current_size{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "range": true, "refId": "A"}], "title": "当前线程数", "type": "timeseries"}, {"datasource": null, "description": "活跃线程数", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 5}, "id": 8, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["min", "max", "last"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_active_count{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "range": true, "refId": "A"}], "title": "活跃线程数", "type": "timeseries"}, {"datasource": null, "description": "任务队列大小", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 13}, "id": 6, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "thread_pool_queue_size{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "任务队列大小", "type": "timeseries"}, {"datasource": null, "description": "任务队列剩余大小", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "orange", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 13}, "id": 2, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["min", "max", "last"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "thread_pool_queue_remaining_capacity{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "B"}], "title": "任务队列剩余大小", "type": "timeseries"}, {"datasource": null, "description": "完成任务数", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 21}, "id": 30, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "thread_pool_completed_task_count{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "format": "time_series", "hide": false, "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "B"}], "title": "完成任务数", "type": "timeseries"}, {"datasource": null, "description": "拒绝任务数", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 21}, "id": 4, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "thread_pool_reject_count{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "format": "time_series", "hide": false, "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "B"}], "title": "拒绝任务数", "type": "timeseries"}, {"datasource": null, "description": "执行超时任务数", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 29}, "id": 14, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "thread_pool_run_timeout_count{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "执行超时任务数", "type": "timeseries"}, {"datasource": null, "description": "排队超时任务数", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 29}, "id": 18, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "thread_pool_queue_timeout_count{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "排队超时任务数", "type": "timeseries"}, {"datasource": null, "description": "tps", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 37}, "id": 31, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_tps{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "range": true, "refId": "A"}], "title": "tps", "type": "timeseries"}, {"datasource": null, "description": "tp999 耗时", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 37}, "id": 16, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_completed_task_time_tp999{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "range": true, "refId": "A"}], "title": "tp999 耗时", "type": "timeseries"}, {"datasource": null, "description": "tp99 耗时", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 45}, "id": 19, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_completed_task_time_tp99{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "range": true, "refId": "A"}], "title": "tp99 耗时", "type": "timeseries"}, {"datasource": null, "description": "tp95 耗时", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 45}, "id": 20, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_completed_task_time_tp95{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "range": true, "refId": "A"}], "title": "tp95 耗时", "type": "timeseries"}, {"datasource": null, "description": "tp90 耗时", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 53}, "id": 21, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_completed_task_time_tp90{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "range": true, "refId": "A"}], "title": "tp90 耗时", "type": "timeseries"}, {"datasource": null, "description": "tp50 耗时", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 53}, "id": 22, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_completed_task_time_tp50{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "range": true, "refId": "A"}], "title": "tp50 耗时", "type": "timeseries"}, {"datasource": null, "description": "任务平均耗时", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 61}, "id": 23, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_completed_task_time_avg{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "range": true, "refId": "A"}], "title": "任务平均耗时", "type": "timeseries"}, {"datasource": null, "description": "任务最大耗时", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 5, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 61}, "id": 24, "links": [{"title": "https://github.com/dromara/dynamic-tp", "url": "https://github.com/dromara/dynamic-tp"}], "options": {"legend": {"calcs": ["last", "min", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "thread_pool_completed_task_time_max{app_name=\"$application\", thread_pool_name=\"$threadpool\"}", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "range": true, "refId": "A"}], "title": "任务最大耗时", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false}, "datasource": null, "definition": "label_values(thread_pool_current_size, kubernetes_namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(thread_pool_current_size, kubernetes_namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false}, "datasource": null, "definition": "label_values(thread_pool_current_size{kubernetes_namespace=\"$namespace\"}, app_name)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "application", "multi": false, "name": "application", "options": [], "query": {"query": "label_values(thread_pool_current_size{kubernetes_namespace=\"$namespace\"}, app_name)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false}, "datasource": null, "definition": "label_values(thread_pool_current_size{app_name=\"$application\"}, thread_pool_name)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "threadpool", "multi": false, "name": "threadpool", "options": [], "query": {"query": "label_values(thread_pool_current_size{app_name=\"$application\"}, thread_pool_name)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "线程池监控（DynamicTp）", "version": 29}