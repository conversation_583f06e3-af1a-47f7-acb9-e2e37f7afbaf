server:
  port: 8888

spring:
  application:
    name: dynamic-tp-nacos-demo
  profiles:
    active: dev

nacos:
  config:
    server-addr: app.wang1.cc:9848
    type: yaml
    data-ids: dynamic-tp-nacos-demo-dev.yml,dynamic-tp-nacos-demo-dtp-dev.yml   # 线程池配置文件必须要在此处配置
    auto-refresh: true
    group: DEFAULT_GROUP
    bootstrap:
      enable: true
      log-enable: true
    namespace: demo

# 开启 SpringBoot Actuator Endpoint 暴露出DynamicTp指标接口
# 开启 prometheus 指标采集端点
management:
  metrics:
    export:
      prometheus:
        enabled: true
  endpoints:
    web:
      exposure:
        include: '*'   # 测试使用，线上不要用*，按需开启