server:
  port: 9103

spring:
  application:
    name: dynamic-tp-adapter-rabbitmq-demo
  profiles:
    active: dev

  rabbitmq:
    host: 127.0.0.1
    port: 5672
    #virtual-host: /playlet-channel
    #username: rabbit
    #password: rabbit
    #addresses: 111111

dynamictp:
  enabled: true
  enabledCollect: true          # 是否开启监控指标采集，默认false
  collectorTypes: internal_logging       # 监控数据采集器类型（logging | micrometer | internal_logging），默认micrometer
  monitorInterval: 5
  rabbitmqTp: # rabbitmq 线程池配置 配置自行修改
    - threadPoolName: rabbitConnectionFactoryTp
      threadPoolAliasName: rabbit线程池
      threadNamePrefix: rabbitExecutor
      corePoolSize: 8
      maximumPoolSize: 16
      #单位秒
      keepAliveTime: 60

# 开启 SpringBoot Actuator Endpoint 暴露出DynamicTp指标接口
# 开启 prometheus 指标采集端点
management:
  metrics:
    export:
      prometheus:
        enabled: true
  endpoints:
    web:
      exposure:
        include: '*'   # 测试使用，线上不要用*，按需开启