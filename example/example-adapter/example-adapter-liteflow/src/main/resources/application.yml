server:
  port: 9119

liteflow:
  rule-source: config/flow.el.xml

spring:
  application:
    name: dynamic-tp-adapter-liteflow-demo
  profiles:
    active: dev

dynamictp:
  enabled: true
  enabledCollect: true          # 是否开启监控指标采集，默认false
  collectorTypes: logging       # 监控数据采集器类型（logging | micrometer | internal_logging），默认micrometer
  monitorInterval: 5
  liteflowTp:
    - threadPoolName: liteflowTp#LiteFlowDefaultWhenExecutorBuilder
      corePoolSize: 10
      maximumPoolSize: 20
      keepAliveTime: 60

# 开启 SpringBoot Actuator Endpoint 暴露出DynamicTp指标接口
# 开启 prometheus 指标采集端点
management:
  metrics:
    export:
      prometheus:
        enabled: true
  endpoints:
    web:
      exposure:
        include: '*'   # 测试使用，线上不要用*，按需开启