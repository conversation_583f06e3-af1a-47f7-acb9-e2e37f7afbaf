syntax = "proto3";

option java_multiple_files = true;
option java_package = "org.dromara.dynamictp.example.grpc";
option java_outer_classname = "HelloWorldProto";

// The greeting service definition.
service Simple {
    // Sends a greeting
    rpc <PERSON><PERSON><PERSON> (HelloRequest) returns (HelloReply) {
    }
}

// The request message containing the user's name.
message HelloRequest {
    string name = 1;
}

// The response message containing the greetings
message HelloReply {
    string message = 1;
}