server:
  port: 9101

spring:
  application:
    name: dynamic-tp-adapter-okhttp3-demo
  profiles:
    active: dev

dynamictp:
  enabled: true
  enabledCollect: true          # 是否开启监控指标采集，默认false
  collectorTypes: logging       # 监控数据采集器类型（logging | micrometer | internal_logging），默认micrometer
  monitorInterval: 5
  platforms:                    # 通知报警平台配置
    - platform: wechat
      urlKey: 32d7bf40-1353              # 替换
      receivers: test1,test2                   # 接受人企微名称
    - platform: ding
      urlKey: f80dad441fcd655438f4a08dcd6a     # 替换
      secret: SECb5441fa6f375d5b9d21           # 替换，非sign模式可以没有此值
      receivers: 15810119805                   # 钉钉账号手机号
    - platform: lark
      urlKey: 0d944ae7-b24a-40                 # 替换
      receivers: test1,test2                   # 接受人飞书名称/openid
  okhttp3Tp:                                   # okhttp3 线程池配置
    - threadPoolName: okHttpClientTp
      corePoolSize: 1
      maximumPoolSize: 1
      keepAliveTime: 60
      runTimeout: 20
      queueTimeout: 10
  tomcatTp:                                    # tomcat webserver 线程池配置
    corePoolSize: 1
    maximumPoolSize: 1
    keepAliveTime: 60
    runTimeout: 20
    queueTimeout: 10

# 开启 SpringBoot Actuator Endpoint 暴露出DynamicTp指标接口
# 开启 prometheus 指标采集端点
management:
  metrics:
    export:
      prometheus:
        enabled: true
  endpoints:
    web:
      exposure:
        include: '*'   # 测试使用，线上不要用*，按需开启