<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>dynamic-tp-example-adapter-dubbo</artifactId>
        <groupId>org.dromara.dynamictp</groupId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>dynamic-tp-example-adapter-apache-dubbo</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>example-adapter-apache-dubbo-2.7.3</module>
        <module>example-adapter-apache-dubbo-2.7.9</module>
        <module>example-adapter-apache-dubbo-3.0.7</module>
        <module>example-adapter-apache-dubbo-3.2.10</module>
    </modules>
</project>