<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.dromara.dynamictp</groupId>
        <artifactId>dynamic-tp-example</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>dynamic-tp-example-huawei-cloud</artifactId>

    <properties>
        <spring-cloud.version>2020.0.6</spring-cloud.version>
        <spring-cloud-huawei.version>1.10.8-2020.0.x</spring-cloud-huawei.version>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- configure user spring cloud / spring boot versions -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- configure spring cloud huawei version -->
            <dependency>
                <groupId>com.huaweicloud</groupId>
                <artifactId>spring-cloud-huawei-bom</artifactId>
                <version>${spring-cloud-huawei.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>com.huaweicloud</groupId>
            <artifactId>spring-cloud-starter-huawei-config</artifactId>
        </dependency>

        <dependency>
            <groupId>org.dromara.dynamictp</groupId>
            <artifactId>dynamic-tp-spring-boot-starter-adapter-webserver</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>org.dromara.dynamictp</groupId>
            <artifactId>dynamic-tp-spring-cloud-starter-huawei</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>

</project>