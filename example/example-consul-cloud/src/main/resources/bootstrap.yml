server:
  port: 9100

spring:
  datasource:
    url: ********************************
    username: root
    password:
  application:
    name: dynamic-tp-cloud-consul-demo
  profiles:
    active: dev
  cloud:
    consul:
      host: 127.0.0.1
      port: 8500
      config:
        enabled: true
        prefixes: config
        format: yaml
        data-key: dynamic-tp-cloud-consul-demo-dtp

# 开启 SpringBoot Actuator Endpoint 暴露出DynamicTp指标接口
# 开启 prometheus 指标采集端点
management:
  metrics:
    export:
      prometheus:
        enabled: true
  endpoints:
    web:
      exposure:
        include: '*'   # 测试使用，线上不要用*，按需开启

mybatis:
  mapper-locations: classpath:mapper/*.xml
