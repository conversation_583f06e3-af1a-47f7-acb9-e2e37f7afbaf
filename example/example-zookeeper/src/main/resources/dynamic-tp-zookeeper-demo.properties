dynamictp.enabled=true
dynamictp.enabledBanner=true
dynamictp.enabledCollect=true
dynamictp.collectorType=logging
dynamictp.monitorInterval=5
dynamictp.executors[0].threadPoolName=dynamic-tp-test-1
dynamictp.executors[0].corePoolSize=50
dynamictp.executors[0].maximumPoolSize=50
dynamictp.executors[0].queueCapacity=3000
dynamictp.executors[0].queueType=VariableLinkedBlockingQueue
dynamictp.executors[0].rejectedHandlerType=CallerRunsPolicy
dynamictp.executors[0].keepAliveTime=50
dynamictp.executors[0].allowCoreThreadTimeOut=false
dynamictp.executors[0].threadNamePrefix=test1
dynamictp.executors[0].notifyItems[0].type=capacity
dynamictp.executors[0].notifyItems[0].enabled=false
dynamictp.executors[0].notifyItems[0].threshold=80
dynamictp.executors[0].notifyItems[0].platforms[0]=ding
dynamictp.executors[0].notifyItems[0].platforms[1]=wechat
dynamictp.executors[0].notifyItems[0].interval=120
dynamictp.executors[0].notifyItems[1].type=change
dynamictp.executors[0].notifyItems[1].enabled=false
dynamictp.executors[0].notifyItems[2].type=liveness
dynamictp.executors[0].notifyItems[2].enabled=false
dynamictp.executors[0].notifyItems[2].threshold=80
dynamictp.executors[0].notifyItems[3].type=reject
dynamictp.executors[0].notifyItems[3].enabled=false
dynamictp.executors[0].notifyItems[3].threshold=1
dynamictp.executors[1].threadPoolName=dynamic-tp-test-2
dynamictp.executors[1].corePoolSize=20
dynamictp.executors[1].maximumPoolSize=30
dynamictp.executors[1].queueCapacity=1000
dynamictp.executors[1].queueType=VariableLinkedBlockingQueue
dynamictp.executors[1].rejectedHandlerType=CallerRunsPolicy
dynamictp.executors[1].keepAliveTime=50
dynamictp.executors[1].allowCoreThreadTimeOut=false
dynamictp.executors[1].threadNamePrefix=test2
dynamictp.executors[1].notifyItems[0].type=capacity
dynamictp.executors[1].notifyItems[0].enabled=false
dynamictp.executors[1].notifyItems[0].threshold=80
dynamictp.executors[1].notifyItems[0].platforms[0]=ding
dynamictp.executors[1].notifyItems[0].platforms[1]=wechat
dynamictp.executors[1].notifyItems[0].interval=120
dynamictp.executors[1].notifyItems[1].type=change
dynamictp.executors[1].notifyItems[1].enabled=false
dynamictp.executors[1].notifyItems[2].type=liveness
dynamictp.executors[1].notifyItems[2].enabled=false
dynamictp.executors[1].notifyItems[2].threshold=80
dynamictp.executors[1].notifyItems[3].type=reject
dynamictp.executors[1].notifyItems[3].enabled=false
dynamictp.executors[1].notifyItems[3].threshold=1
