{"enabled": true, "collectorType": "logging", "monitorInterval": 5, "enabledBanner": true, "enabledCollect": true, "configType": "json", "zookeeper": {"zkConnectStr": "127.0.0.1:2181", "rootNode": "/configserver/dev", "node": "dynamic-tp-zookeeper-demo", "config-key": "dtp-config"}, "platforms": [{"platform": "ding", "urlKey": "aab197577f6d8dcea6f\t", "receivers": "所有人"}], "executors": [{"threadPoolName": "dtpExecutor1", "executorType": "common", "keepAliveTime": 20, "waitForTasksToCompleteOnShutdown": false, "rejectedHandlerType": "AbortPolicy", "queueCapacity": 1000, "fair": false, "unit": "SECONDS", "runTimeout": 300, "threadNamePrefix": "t0", "allowCoreThreadTimeOut": false, "corePoolSize": 15, "queueType": "VariableLinkedBlockingQueue", "maximumPoolSize": 30, "awaitTerminationSeconds": 1, "preStartAllCoreThreads": true, "notifyItems": [], "queueTimeout": 300}, {"threadPoolName": "dtpExecutor2", "executorType": "common", "keepAliveTime": 20, "waitForTasksToCompleteOnShutdown": false, "rejectedHandlerType": "AbortPolicy", "queueCapacity": 1000, "fair": false, "unit": "SECONDS", "runTimeout": 300, "threadNamePrefix": "t1", "allowCoreThreadTimeOut": false, "corePoolSize": 20, "queueType": "VariableLinkedBlockingQueue", "maximumPoolSize": 20, "awaitTerminationSeconds": 1, "preStartAllCoreThreads": true, "notifyItems": [], "queueTimeout": 300}]}