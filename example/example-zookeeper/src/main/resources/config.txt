/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.enabled=true
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.enabledBanner=true
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.enabledCollect=true
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.collectorType=logging
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.monitorInterval=5
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].threadPoolName=dtpExecutor1
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].corePoolSize=50
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].maximumPoolSize=50
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].queueCapacity=3000
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].queueType=VariableLinkedBlockingQueue
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].rejectedHandlerType=CallerRunsPolicy
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].keepAliveTime=50
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].allowCoreThreadTimeOut=false
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].threadNamePrefix=test1
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].notifyItems[0].type=capacity
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].notifyItems[0].enabled=false
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].notifyItems[0].threshold=80
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].notifyItems[0].platforms[0]=ding
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].notifyItems[0].platforms[1]=wechat
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].notifyItems[0].interval=120
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].notifyItems[1].type=change
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].notifyItems[1].enabled=false
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].notifyItems[2].type=liveness
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].notifyItems[2].enabled=false
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].notifyItems[2].threshold=80
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].notifyItems[3].type=reject
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].notifyItems[3].enabled=false
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[0].notifyItems[3].threshold=1
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].threadPoolName=dtpExecutor2
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].corePoolSize=20
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].maximumPoolSize=30
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].queueCapacity=1000
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].queueType=VariableLinkedBlockingQueue
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].rejectedHandlerType=CallerRunsPolicy
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].keepAliveTime=50
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].allowCoreThreadTimeOut=false
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].threadNamePrefix=test2
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].notifyItems[0].type=capacity
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].notifyItems[0].enabled=false
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].notifyItems[0].threshold=80
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].notifyItems[0].platforms[0]=ding
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].notifyItems[0].platforms[1]=wechat
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].notifyItems[0].interval=120
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].notifyItems[1].type=change
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].notifyItems[1].enabled=false
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].notifyItems[2].type=liveness
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].notifyItems[2].enabled=false
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].notifyItems[2].threshold=80
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].notifyItems[3].type=reject
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].notifyItems[3].enabled=false
/configserver/dev/dynamic-tp-zookeeper-demo=dynamictp.executors[1].notifyItems[3].threshold=1
