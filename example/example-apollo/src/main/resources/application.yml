server:
  port: 8888

spring:
  application:
    name: dynamic-tp-apollo-demo
  profiles:
    active: dev

apollo:
  bootstrap:
    enabled: true
    eagerLoad:
      enabled: true
    namespaces: dynamic-tp-apollo-demo-dtp.yml,application
  meta: http://81.68.181.139:8080
app:
  id: dynamic-tp-apollo-demo

# 开启 SpringBoot Actuator Endpoint 暴露出DynamicTp指标接口
# 开启 prometheus 指标采集端点
management:
  metrics:
    export:
      prometheus:
        enabled: true
  endpoints:
    web:
      exposure:
        include: '*'   # 测试使用，线上不要用*，按需开启
