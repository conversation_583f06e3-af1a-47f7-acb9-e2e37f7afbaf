{"enabled": true, "collectorType": "logging", "monitorInterval": 5, "enabledBanner": true, "enabledCollect": true, "configType": "json", "platforms": [{"platform": "ding", "urlKey": "b46b246d5dacf725f273a4e7b3f6873a8a4da84ec22bf7b06a25591f507040a1", "receivers": "所有人"}], "executors": [{"threadPoolName": "dtpExecutor1", "executorType": "common", "keepAliveTime": 20, "waitForTasksToCompleteOnShutdown": false, "rejectedHandlerType": "AbortPolicy", "queueCapacity": 1000, "fair": false, "unit": "SECONDS", "runTimeout": 300, "threadNamePrefix": "t0", "allowCoreThreadTimeOut": false, "corePoolSize": 120, "queueType": "VariableLinkedBlockingQueue", "maximumPoolSize": 120, "awaitTerminationSeconds": 1, "preStartAllCoreThreads": true, "notifyItems": [], "queueTimeout": 300}]}