server:
  port: 9100

spring:
  application:
    name: dynamic-tp-nacos-cloud-demo
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: http://${nacos:localhost}:8848
      config:
        server-addr: http://${nacos:localhost}:8848
        file-extension: yml
        extension-configs:
          - dataId: ${spring.application.name}-dtp-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: DEFAULT_GROUP
            refresh: true  # 必须配置，负责自动刷新不生效
        refresh-enabled: true
  redis:
    host: ${redis:localhost}
    port: 6379

# 开启 SpringBoot Actuator Endpoint 暴露出DynamicTp指标接口
# 开启 prometheus 指标采集端点
management:
  metrics:
    export:
      prometheus:
        enabled: true
  endpoints:
    web:
      exposure:
        include: '*'   # 测试使用，线上不要用*，按需开启