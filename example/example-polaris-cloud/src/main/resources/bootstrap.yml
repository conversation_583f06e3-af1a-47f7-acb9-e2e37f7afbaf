server:
  port: 9018

spring:
  application:
    name: dynamic-tp-polaris-cloud-demo
  cloud:
    polaris:
      address: grpc://*************:8091
      namespace: default # 设置配置中心命名空间
      discovery:
        enabled: true
      stat:
        enabled: true
        port: 28082
      config:
        auto-refresh: true # auto refresh when config file changed
        groups:
          - name: ${spring.application.name} # group name
            files: [ "config/dynamic-tp.yml" ]

# 开启 SpringBoot Actuator Endpoint 暴露出DynamicTp指标接口
# 开启 prometheus 指标采集端点
management:
  metrics:
    export:
      prometheus:
        enabled: true
  endpoints:
    web:
      exposure:
        include: '*'   # 测试使用，线上不要用*，按需开启