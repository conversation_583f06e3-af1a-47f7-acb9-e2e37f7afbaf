name: build jvmti

on: [push]

jobs:
  linux:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up JDK 8
        uses: actions/setup-java@v3
        with:
          java-version: '8'
          distribution: 'adopt'
      - name: Build with <PERSON><PERSON>
        run: |
          cd ${{ github.workspace }}/jvmti/jvmti-build
          mvn package
      - uses: actions/upload-artifact@v4
        with:
          name: lib-linux
          path: jvmti/jvmti-build/target/classes/lib*
          if-no-files-found: error

  mac:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'adopt'
      - name: Build with <PERSON><PERSON>
        run: |
          cd ${{ github.workspace }}/jvmti/jvmti-build
          mvn package
      - uses: actions/upload-artifact@v4
        with:
          name: lib-macos
          path: jvmti/jvmti-build/target/classes/lib*
          if-no-files-found: error

  windows:
    runs-on: windows-2019
    steps:
      - uses: actions/checkout@v3
      - name: Set up JDK 8
        uses: actions/setup-java@v3
        with:
          java-version: '8'
          distribution: 'adopt'
      - name: Build with <PERSON>ven
        run: |
          cd ${{ github.workspace }}/jvmti/jvmti-build
          mvn package
      - uses: actions/upload-artifact@v4
        with:
          name: lib-windows
          path: jvmti/jvmti-build/target/classes/*.dll
          if-no-files-found: error
