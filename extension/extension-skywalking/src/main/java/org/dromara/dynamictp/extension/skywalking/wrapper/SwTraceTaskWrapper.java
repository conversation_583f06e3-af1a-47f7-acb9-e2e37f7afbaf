/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.extension.skywalking.wrapper;

import org.dromara.dynamictp.core.support.task.runnable.MdcRunnable;
import org.dromara.dynamictp.core.support.task.wrapper.TaskWrapper;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.slf4j.MDC;

import static org.dromara.dynamictp.common.constant.DynamicTpConst.TRACE_ID;

/**
 * SwTraceTaskWrapper related
 *
 * <AUTHOR>
 * @since 1.0.8
 **/
public class SwTraceTaskWrapper implements TaskWrapper {

    private static final String NAME = "swTrace";

    @Override
    public String name() {
        return NAME;
    }

    @Override
    public Runnable wrap(Runnable runnable) {
        MDC.put(TRACE_ID, TraceContext.traceId());
        return MdcRunnable.get(new RunnableWrapper(runnable));
    }
}
