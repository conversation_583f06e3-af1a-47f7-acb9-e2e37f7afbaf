<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.dromara.dynamictp</groupId>
        <artifactId>dynamic-tp-all</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>dynamic-tp-extension</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>extension-skywalking</module>
        <module>extension-limiter-redis</module>
        <module>extension-notify-email</module>
        <module>extension-opentelemetry</module>
        <module>extension-notify-yun<PERSON>jia</module>
        <module>extension-agent</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.dromara.dynamictp</groupId>
            <artifactId>dynamic-tp-core</artifactId>
        </dependency>
    </dependencies>
</project>
