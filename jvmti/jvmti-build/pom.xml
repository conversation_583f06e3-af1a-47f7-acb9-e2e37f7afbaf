<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.dromara.dynamictp</groupId>
        <artifactId>dynamic-tp-jvmti</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>dynamic-tp-jvmti-build</artifactId>

    <profiles>
        <!-- https://github.com/openjdk/jdk/blob/jdk-16%2B36/src/java.base/windows/native/libjava/java_props_md.c#L568 -->
        <!-- macos -->
        <profile>
            <id>macos-amd64</id>
            <activation>
                <os>
                    <family>mac</family>
                </os>
            </activation>
            <properties>
                <os_name>darwin</os_name>
                <os_arch_option>-arch x86_64 -arch arm64</os_arch_option>
                <lib_name>libJniLibrary.dylib</lib_name>
            </properties>
        </profile>

        <!-- linux -->
        <profile>
            <id>linux-amd64</id>
            <activation>
                <os>
                    <name>linux</name>
                    <arch>amd64</arch>
                </os>
            </activation>
            <properties>
                <os_name>linux</os_name>
                <os_arch_option>-m64</os_arch_option>
                <lib_name>libJniLibrary-x64.so</lib_name>
            </properties>
        </profile>
        <profile>
            <id>linux-aarch64</id>
            <activation>
                <os>
                    <name>linux</name>
                    <arch>aarch64</arch>
                </os>
            </activation>
            <properties>
                <os_name>linux</os_name>
                <os_arch_option>-march=armv8-a</os_arch_option>
                <lib_name>libJniLibrary-aarch64.so</lib_name>
            </properties>
        </profile>

        <!-- linux other-arch-->
        <profile>
            <id>linux-${os.arch}</id>
            <activation>
                <os>
                    <name>linux</name>
                    <arch>!amd64</arch>
                </os>
            </activation>
            <properties>
                <os_name>linux</os_name>
                <lib_name>libJniLibrary-${os.arch}.so</lib_name>
            </properties>
        </profile>

        <!-- windows -->
        <profile>
            <id>windows</id>
            <activation>
                <os>
                    <family>windows</family>
                </os>
            </activation>
            <properties>
                <!-- https://github.com/mojohaus/maven-native/blob/maven-native-1.0-alpha-11/native-maven-plugin/src/site/apt/examples/jni-dll.apt#L67-->
                <os_name>win32</os_name>
            </properties>
        </profile>
        <profile>
            <id>windows-32</id>
            <activation>
                <os>
                    <family>windows</family>
                    <arch>x86</arch>
                </os>
            </activation>
            <properties>
                <os_arch_option>-m32</os_arch_option>
                <lib_name>libJniLibrary-x86.dll</lib_name>
            </properties>
        </profile>
        <profile>
            <id>windows-amd64</id>
            <activation>
                <os>
                    <family>windows</family>
                    <arch>amd64</arch>
                </os>
            </activation>
            <properties>
                <os_arch_option>-m64</os_arch_option>
                <lib_name>libJniLibrary-x64.dll</lib_name>
            </properties>
            <build>
                <plugins>

                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>native-maven-plugin</artifactId>
                        <version>1.0-alpha-11</version>
                        <extensions>true</extensions>
                        <configuration>
                            <javahIncludes>
                                <javahInclude>
                                    <className>org.dromara.dynamictp.jvmti.JVMTI</className>
                                </javahInclude>
                            </javahIncludes>
                            <javahOS>${os_name}</javahOS>
                            <sources>
                                <source>
                                    <directory>src/main/native/src</directory>
                                    <fileNames>
                                        <fileName>jni-library.cpp</fileName>
                                    </fileNames>
                                </source>
                            </sources>

                            <compilerProvider>generic-classic</compilerProvider>
                            <compilerExecutable>g++</compilerExecutable>
                            <compilerStartOptions>
                                <compilerStartOption>-I${JAVA_HOME}/include</compilerStartOption>
                                <compilerStartOption>-I${JAVA_HOME}/include/${os_name}</compilerStartOption>
                                <compilerStartOption>-I${project.build.directory}/native/include</compilerStartOption>
                                <compilerStartOption>${os_arch_option}</compilerStartOption>
                                <compilerStartOption>-fpic</compilerStartOption>
                                <compilerStartOption>-shared</compilerStartOption>
                                <compilerStartOption>-o</compilerStartOption>
                            </compilerStartOptions>

                            <linkerOutputDirectory>target</linkerOutputDirectory>
                            <linkerExecutable>g++</linkerExecutable>
                            <linkerStartOptions>
                                <linkerStartOption>${os_arch_option}</linkerStartOption>
                                <linkerStartOption>-fpic</linkerStartOption>
                                <linkerStartOption>-shared</linkerStartOption>
                                <linkerStartOption>-o</linkerStartOption>
                                <!-- for windows https://github.com/alibaba/arthas/issues/1833 -->
                                <linkerStartOption>-static-libstdc++</linkerStartOption>
                                <linkerStartOption>-static</linkerStartOption>
                            </linkerStartOptions>
                            <linkerEndOptions>
                                <linkerEndOption>-o ${project.build.directory}/classes/${lib_name}</linkerEndOption>
                            </linkerEndOptions>
                        </configuration>
                        <executions>
                            <execution>
                                <id>compile-and-link</id>
                                <phase>compile</phase>
                                <goals>
                                    <goal>initialize</goal>
                                    <goal>compile</goal>
                                    <goal>link</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <compilerArgs>
                        <!-- 需要使用 3.8.0 及之后版本，使用javac -h 生成头文件 -->
                        <arg>-h</arg>
                        <arg>${project.build.directory}/native/include</arg>
                    </compilerArgs>
                </configuration>
            </plugin>

            <!-- 请不要删除这里的maven-jar-plugin，也不要升级它的版本，否则很可能无法正常打包 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.4</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>native-maven-plugin</artifactId>
                <version>1.0-alpha-11</version>
                <extensions>true</extensions>
                <configuration>
                    <javahIncludes>
                        <javahInclude>
                            <className>org.dromara.dynamictp.jvmti.JVMTI</className>
                        </javahInclude>
                    </javahIncludes>
                    <sources>
                        <source>
                            <directory>src/main/native/src</directory>
                            <fileNames>
                                <fileName>jni-library.cpp</fileName>
                            </fileNames>
                        </source>
                    </sources>

                    <compilerProvider>generic-classic</compilerProvider>
                    <compilerExecutable>g++</compilerExecutable>
                    <compilerStartOptions>
                        <compilerStartOption>-I${JAVA_HOME}/include</compilerStartOption>
                        <compilerStartOption>-I${JAVA_HOME}/include/${os_name}</compilerStartOption>
                        <compilerStartOption>-I${project.build.directory}/native/include</compilerStartOption>
                        <compilerStartOption>${os_arch_option}</compilerStartOption>
                        <compilerStartOption>-fpic</compilerStartOption>
                        <compilerStartOption>-shared</compilerStartOption>
                        <compilerStartOption>-o</compilerStartOption>
                    </compilerStartOptions>

                    <linkerOutputDirectory>target</linkerOutputDirectory>
                    <linkerExecutable>g++</linkerExecutable>
                    <linkerStartOptions>
                        <linkerStartOption>${os_arch_option}</linkerStartOption>
                        <linkerStartOption>-fpic</linkerStartOption>
                        <linkerStartOption>-shared</linkerStartOption>
                        <!-- <linkerStartOption>-o</linkerStartOption> -->
                    </linkerStartOptions>
                    <linkerEndOptions>
                        <linkerEndOption>-o ${project.build.directory}/classes/${lib_name}</linkerEndOption>
                    </linkerEndOptions>
                </configuration>
                <executions>
                    <execution>
                        <id>compile-and-link</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>initialize</goal>
                            <goal>compile</goal>
                            <goal>link</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>