<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.dromara.dynamictp</groupId>
    <artifactId>dynamic-tp-dependencies</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <name>dynamic-tp-dependencies</name>
    <description>🔥🔥🔥轻量级动态线程池，内置监控告警功能，基于主流配置中心（已支持Nacos、Apollo、Zookeeper、Consul、Etcd、Polaris，可通过SPI自定义实现）</description>
    <url>https://github.com/yanhom1314/dynamic-tp</url>

    <properties>
        <revision>1.2.1</revision>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <lombok.version>1.18.24</lombok.version>
        <slf4j.version>1.7.36</slf4j.version>
        <logback.version>1.2.10</logback.version>
        <log4j.version>2.17.1</log4j.version>

        <hutool.version>5.8.25</hutool.version>
        <guava.version>31.1-jre</guava.version>
        <jackson.version>2.13.5</jackson.version>
        <gson.version>2.8.9</gson.version>
        <fastjson.version>1.2.83</fastjson.version>

        <ttl.version>2.14.5</ttl.version>
        <equator.version>1.0.4</equator.version>

        <sofa-rpc.version>5.12.0</sofa-rpc.version>
        <liteflow.version>2.12.3</liteflow.version>
        <brpc.version>2022.2.0</brpc.version>
        <apache-dubbo.version>3.0.14</apache-dubbo.version>
        <alibaba-dubbo.version>2.6.10</alibaba-dubbo.version>
        <alibaba-dubbo-starter.version>2.0.0</alibaba-dubbo-starter.version>
        <grpc-starter.version>2.15.0.RELEASE</grpc-starter.version>
        <hystrix.version>1.5.18</hystrix.version>
        <motan.version>1.2.0</motan.version>
        <okhttp.version>3.14.9</okhttp.version>
        <rocketmq-starter.version>2.2.2</rocketmq-starter.version>
        <rocketmq.version>4.9.3</rocketmq.version>
        <!--until 2023-04-06 latest state version-->
        <aliyun-rocketmq.version>1.8.8.8.Final</aliyun-rocketmq.version>
        <tars.version>1.7.3</tars.version>
        <skywalking.version>9.1.0</skywalking.version>
        <opentelemetry.version>1.25.0</opentelemetry.version>

        <apollo.version>2.0.0</apollo.version>
        <nacos-api.version>2.0.4</nacos-api.version>
        <spring-boot-nacos.version>0.2.12</spring-boot-nacos.version>
        <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
        <spring-cloud-tencent.version>1.12.4-2021.0.8</spring-cloud-tencent.version>
        <spring-cloud-huawei.version>1.10.11-2021.0.x</spring-cloud-huawei.version>
        <zookeeper.version>5.2.1</zookeeper.version>

        <maven-flatten.version>1.7.0</maven-flatten.version>
        <maven-source-plugin.version>2.4</maven-source-plugin.version>
        <maven-javadoc-plugin.version>3.2.0</maven-javadoc-plugin.version>
        <maven-gpg-plugin.version>1.6</maven-gpg-plugin.version>
        <central-publishing-maven-plugin.version>0.6.0</central-publishing-maven-plugin.version>

        <dropwizard-metrics.version>4.2.20</dropwizard-metrics.version>
        <jmh.version>1.36</jmh.version>
        <native-lib-loader.version>2.0.2</native-lib-loader.version>
        <bytebuddy.version>1.17.5</bytebuddy.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-api</artifactId>
                <version>${nacos-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-core</artifactId>
                <version>${hystrix.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${apache-dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${apache-dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dubbo</artifactId>
                <version>${alibaba-dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.spring.boot</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${alibaba-dubbo-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>net.devh</groupId>
                <artifactId>grpc-spring-boot-starter</artifactId>
                <version>${grpc-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-http</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.openservices</groupId>
                <artifactId>ons-client</artifactId>
                <version>${aliyun-rocketmq.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-api</artifactId>
                <version>${opentelemetry.version}</version>
            </dependency>

            <dependency>
                <groupId>com.weibo</groupId>
                <artifactId>motan-core</artifactId>
                <version>${motan.version}</version>
            </dependency>
            <dependency>
                <groupId>com.weibo</groupId>
                <artifactId>motan-transport-netty</artifactId>
                <version>${motan.version}</version>
            </dependency>

            <dependency>
                <groupId>com.weibo</groupId>
                <artifactId>motan-springsupport</artifactId>
                <version>${motan.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tencent.tars</groupId>
                <artifactId>tars-core</artifactId>
                <version>${tars.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-rpc-all</artifactId>
                <version>${sofa-rpc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>liteflow-core</artifactId>
                <version>${liteflow.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${zookeeper.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.cloud</groupId>
                <artifactId>starlight-all</artifactId>
                <version>${brpc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.cloud</groupId>
                <artifactId>spring-cloud-starter-baidu-starlight</artifactId>
                <version>${brpc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.openjdk.jmh</groupId>
                <artifactId>jmh-core</artifactId>
                <version>${jmh.version}</version>
            </dependency>

            <dependency>
                <groupId>org.openjdk.jmh</groupId>
                <artifactId>jmh-generator-annprocess</artifactId>
                <version>${jmh.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${spring-boot-nacos.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tencent.cloud</groupId>
                <artifactId>spring-cloud-starter-tencent-polaris-config</artifactId>
                <version>${spring-cloud-tencent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huaweicloud</groupId>
                <artifactId>spring-cloud-starter-huawei-config</artifactId>
                <version>${spring-cloud-huawei.version}</version>
            </dependency>

            <dependency>
                <groupId>org.scijava</groupId>
                <artifactId>native-lib-loader</artifactId>
                <version>${native-lib-loader.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${ttl.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.dadiyang</groupId>
                <artifactId>equator</artifactId>
                <version>${equator.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/io.dropwizard.metrics/metrics-core -->
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-core</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/net.bytebuddy/byte-buddy -->
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>${bytebuddy.version}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-jvmti-runtime</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-adapter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-logging</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-adapter-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-adapter-dubbo</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-adapter-rocketmq</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-adapter-rabbitmq</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-adapter-hystrix</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-adapter-grpc</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-adapter-motan</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-adapter-okhttp3</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-adapter-brpc</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-adapter-tars</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-adapter-sofa</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-adapter-liteflow</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-extension-limiter-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-extension-agent</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-extension-notify-email</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-extension-notify-yunzhijia</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-extension-skywalking</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-adapter-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-adapter-webserver</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-apollo</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-zookeeper</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-cloud-starter-zookeeper</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-cloud-starter-consul</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-etcd</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-cloud-starter-polaris</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-cloud-starter-huawei</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-nacos</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-cloud-starter-nacos</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-adapter-dubbo</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-adapter-rocketmq</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-adapter-hystrix</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-extension-limiter-redis</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-extension-notify-email</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-extension-notify-yunzhijia</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-adapter-okhttp3</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-adapter-grpc</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-adapter-brpc</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-adapter-motan</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-adapter-tars</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-adapter-sofa</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-adapter-rabbitmq</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-adapter-liteflow</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <url>https://github.com/yanhom1314/dynamic-tp/tree/master</url>
        <connection>scm:git:git://github.com/yanhom1314/dynamic-tp.git</connection>
        <developerConnection>scm:git:ssh://github.com:yanhom1314/dynamic-tp.git</developerConnection>
        <tag>HEAD</tag>
    </scm>

    <developers>
        <developer>
            <id>yanhom</id>
            <name>yanhom</name>
            <email><EMAIL></email>
            <url>https://github.com/yanhom1314</url>
        </developer>
    </developers>

    <issueManagement>
        <system>Github Issue</system>
        <url>https://github.com/yanhom1314/dynamic-tp/issues</url>
    </issueManagement>

    <distributionManagement>
        <snapshotRepository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven-source-plugin.version}</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>${maven-javadoc-plugin.version}</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Gpg Signature -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-gpg-plugin</artifactId>
                <version>1.6</version>
                <executions>
                    <execution>
                        <phase>verify</phase>
                        <goals>
                            <goal>sign</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.sonatype.central</groupId>
                <artifactId>central-publishing-maven-plugin</artifactId>
                <version>${central-publishing-maven-plugin.version}</version>
                <extensions>true</extensions>
                <configuration>
                    <publishingServerId>ossrh</publishingServerId>
                    <autoPublish>false</autoPublish>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.sonatype.plugins</groupId>
                <artifactId>nexus-staging-maven-plugin</artifactId>
                <version>1.6.7</version>
                <extensions>true</extensions>
                <configuration>
                    <serverId>ossrh</serverId>
                    <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                    <autoReleaseAfterClose>false</autoReleaseAfterClose>
                </configuration>
            </plugin>

            <!-- flatten -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${maven-flatten.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
