<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.dromara.dynamictp</groupId>
        <artifactId>dynamic-tp-starter</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>dynamic-tp-starter-adapter</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>starter-adapter-common</module>
        <module>starter-adapter-dubbo</module>
        <module>starter-adapter-hystrix</module>
        <module>starter-adapter-rocketmq</module>
        <module>starter-adapter-webserver</module>
        <module>starter-adapter-grpc</module>
        <module>starter-adapter-motan</module>
        <module>starter-adapter-okhttp3</module>
        <module>starter-adapter-brpc</module>
        <module>starter-adapter-tars</module>
        <module>starter-adapter-sofa</module>
        <module>starter-adapter-rabbitmq</module>
        <module>starter-adapter-liteflow</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.dromara.dynamictp</groupId>
            <artifactId>dynamic-tp-spring-boot-starter-common</artifactId>
        </dependency>
    </dependencies>
</project>
