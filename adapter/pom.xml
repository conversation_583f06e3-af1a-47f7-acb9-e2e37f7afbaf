<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.dromara.dynamictp</groupId>
        <artifactId>dynamic-tp-all</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>dynamic-tp-adapter</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>adapter-common</module>
        <module>adapter-dubbo</module>
        <module>adapter-rocketmq</module>
        <module>adapter-hystrix</module>
        <module>adapter-grpc</module>
        <module>adapter-motan</module>
        <module>adapter-okhttp3</module>
        <module>adapter-brpc</module>
        <module>adapter-tars</module>
        <module>adapter-sofa</module>
        <module>adapter-rabbitmq</module>
        <module>adapter-liteflow</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.dromara.dynamictp</groupId>
            <artifactId>dynamic-tp-core</artifactId>
        </dependency>
    </dependencies>
</project>
