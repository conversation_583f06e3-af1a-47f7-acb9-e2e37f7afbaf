{"enabled": true, "enabledBanner": true, "enabledCollect": true, "collectorTypes": "micrometer,logging", "logPath": "/home/<USER>", "monitorInterval": 5, "platforms": [{"platform": "wechat", "urlKey": "3a7500-1287-4bd-a798-c5c3d8b69c", "receivers": "test1,test2"}, {"platform": "ding", "urlKey": "f80dad441fcd655438f4a08dcd6a", "secret": "SECb5441fa6f375d5b9d21", "receivers": 15810119805}, {"platform": "lark", "urlKey": "0d944ae7-b24a-40", "receivers": "test1,test2"}], "tomcatTp": {"corePoolSize": 100, "maximumPoolSize": 400, "keepAliveTime": 60}, "jettyTp": {"corePoolSize": 100, "maximumPoolSize": 400}, "undertowTp": {"corePoolSize": 100, "maximumPoolSize": 400, "keepAliveTime": 60}, "hystrixTp": [{"threadPoolName": "hystrix1", "corePoolSize": 100, "maximumPoolSize": 400, "keepAliveTime": 60}], "dubboTp": [{"threadPoolName": "dubboTp#20880", "corePoolSize": 100, "maximumPoolSize": 400, "keepAliveTime": 60}], "rocketMqTp": [{"threadPoolName": "group1#topic1", "corePoolSize": 200, "maximumPoolSize": 400, "keepAliveTime": 60}], "executors": [{"threadPoolName": "dtpExecutor1", "executorType": "common", "corePoolSize": 6, "maximumPoolSize": 8, "queueCapacity": 200, "queueType": "VariableLinkedBlockingQueue", "rejectedHandlerType": "CallerRunsPolicy", "keepAliveTime": 50, "allowCoreThreadTimeOut": false, "threadNamePrefix": "test", "waitForTasksToCompleteOnShutdown": false, "awaitTerminationSeconds": 5, "preStartAllCoreThreads": false, "runTimeout": 200, "queueTimeout": 100, "taskWrapperNames": ["ttl"], "notifyItems": [{"type": "capacity", "enabled": true, "threshold": 80, "platforms": ["ding", "wechat"], "interval": 120}, {"type": "change", "enabled": true}, {"type": "liveness", "enabled": true, "threshold": 80}, {"type": "reject", "enabled": true, "threshold": 1}, {"type": "run_timeout", "enabled": true, "threshold": 1}, {"type": "queue_timeout", "enabled": true, "threshold": 1}]}]}