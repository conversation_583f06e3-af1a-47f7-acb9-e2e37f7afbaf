dynamictp.enabled=true
dynamictp.enabledBanner=true
dynamictp.enabledCollect=true
dynamictp.collectorTypes=micrometer,logging
dynamictp.logPath=/home/<USER>
dynamictp.monitorInterval=5
dynamictp.platforms[0].platform=wechat
dynamictp.platforms[0].urlKey=3a7500-1287-4bd-a798-c5c3d8b69c
dynamictp.platforms[0].receivers=test1,test2
dynamictp.platforms[1].platform=ding
dynamictp.platforms[1].urlKey=f80dad441fcd655438f4a08dcd6a
dynamictp.platforms[1].secret=SECb5441fa6f375d5b9d21
dynamictp.platforms[1].receivers=15810119805
dynamictp.platforms[2].platform=lark
dynamictp.platforms[2].urlKey=0d944ae7-b24a-40
dynamictp.platforms[2].receivers=test1,test2
dynamictp.tomcatTp.corePoolSize=100
dynamictp.tomcatTp.maximumPoolSize=400
dynamictp.tomcatTp.keepAliveTime=60
dynamictp.jettyTp.corePoolSize=100
dynamictp.jettyTp.maximumPoolSize=400
dynamictp.undertowTp.corePoolSize=100
dynamictp.undertowTp.maximumPoolSize=400
dynamictp.undertowTp.keepAliveTime=60
dynamictp.hystrixTp[0].threadPoolName=hystrix1
dynamictp.hystrixTp[0].corePoolSize=100
dynamictp.hystrixTp[0].maximumPoolSize=400
dynamictp.hystrixTp[0].keepAliveTime=60
dynamictp.dubboTp[0].threadPoolName=dubboTp#20880
dynamictp.dubboTp[0].corePoolSize=100
dynamictp.dubboTp[0].maximumPoolSize=400
dynamictp.dubboTp[0].keepAliveTime=60
dynamictp.rocketMqTp[0].threadPoolName=group1#topic1
dynamictp.rocketMqTp[0].corePoolSize=200
dynamictp.rocketMqTp[0].maximumPoolSize=400
dynamictp.rocketMqTp[0].keepAliveTime=60
dynamictp.executors[0].threadPoolName=dtpExecutor1
dynamictp.executors[0].executorType=common
dynamictp.executors[0].corePoolSize=6
dynamictp.executors[0].maximumPoolSize=8
dynamictp.executors[0].queueCapacity=200
dynamictp.executors[0].queueType=VariableLinkedBlockingQueue
dynamictp.executors[0].rejectedHandlerType=CallerRunsPolicy
dynamictp.executors[0].keepAliveTime=50
dynamictp.executors[0].allowCoreThreadTimeOut=false
dynamictp.executors[0].threadNamePrefix=test
dynamictp.executors[0].waitForTasksToCompleteOnShutdown=false
dynamictp.executors[0].awaitTerminationSeconds=5
dynamictp.executors[0].preStartAllCoreThreads=false
dynamictp.executors[0].runTimeout=200
dynamictp.executors[0].queueTimeout=100
dynamictp.executors[0].taskWrapperNames[0]=ttl
dynamictp.executors[0].notifyItems[0].type=capacity
dynamictp.executors[0].notifyItems[0].enabled=true
dynamictp.executors[0].notifyItems[0].threshold=80
dynamictp.executors[0].notifyItems[0].platforms[0]=ding
dynamictp.executors[0].notifyItems[0].platforms[1]=wechat
dynamictp.executors[0].notifyItems[0].interval=120
dynamictp.executors[0].notifyItems[1].type=change
dynamictp.executors[0].notifyItems[1].enabled=true
dynamictp.executors[0].notifyItems[2].type=liveness
dynamictp.executors[0].notifyItems[2].enabled=true
dynamictp.executors[0].notifyItems[2].threshold=80
dynamictp.executors[0].notifyItems[3].type=reject
dynamictp.executors[0].notifyItems[3].enabled=true
dynamictp.executors[0].notifyItems[3].threshold=1
dynamictp.executors[0].notifyItems[4].type=run_timeout
dynamictp.executors[0].notifyItems[4].enabled=true
dynamictp.executors[0].notifyItems[4].threshold=1
dynamictp.executors[0].notifyItems[5].type=queue_timeout
dynamictp.executors[0].notifyItems[5].enabled=true
dynamictp.executors[0].notifyItems[5].threshold=1
