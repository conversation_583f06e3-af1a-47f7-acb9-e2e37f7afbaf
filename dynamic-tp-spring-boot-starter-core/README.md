# Dynamic ThreadPool Spring Boot Starter Core

轻量级动态线程池管理核心starter，提供核心线程池管理功能和Nacos配置中心集成。

## 特性

- 🚀 **轻量级设计**: 专注于核心线程池管理功能，无冗余依赖
- 🔧 **动态配置**: 支持运行时动态调整线程池参数
- 🌐 **Nacos集成**: 内置Nacos配置中心支持，实现配置热更新
- 📊 **统计监控**: 提供详细的线程池运行统计信息
- 🔌 **Spring Boot集成**: 无缝集成Spring Boot，开箱即用
- 🎯 **API友好**: 提供简洁易用的API接口

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>org.dromara.dynamictp</groupId>
    <artifactId>dynamic-tp-spring-boot-starter-core</artifactId>
    <version>2.0.0</version>
</dependency>
```

### 2. 配置线程池

在 `application.yml` 中配置线程池：

```yaml
dynamictp:
  core:
    enabled: true
    enabled-banner: true
    env: dev
    
    # 线程池配置
    executors:
      - thread-pool-name: demoExecutor
        core-pool-size: 2
        maximum-pool-size: 4
        keep-alive-time: 60
        unit: SECONDS
        queue-type: LinkedBlockingQueue
        queue-capacity: 100
        rejected-handler-type: CallerRunsPolicy
        thread-name-prefix: demo-pool
        
    # Nacos配置中心（可选）
    nacos:
      server-addr: 127.0.0.1:8848
      namespace: ""
      group: DEFAULT_GROUP
      data-id: dynamic-tp-config.yml
      type: yaml
```

### 3. 使用线程池

```java
import org.dromara.dynamictp.starter.core.api.DtpCoreApi;
import org.dromara.dynamictp.starter.core.executor.DtpCoreExecutor;

@Service
public class BusinessService {
    
    public void executeAsyncTask() {
        // 获取线程池
        DtpCoreExecutor executor = DtpCoreApi.getExecutor("demoExecutor");
        
        // 提交任务
        executor.execute(() -> {
            // 业务逻辑
            System.out.println("异步任务执行中...");
        });
    }
    
    public void getThreadPoolStats() {
        // 获取线程池统计信息
        DtpCoreExecutor.ThreadPoolStats stats = DtpCoreApi.getThreadPoolStats("demoExecutor");
        System.out.println("活跃线程数: " + stats.getActiveCount());
        System.out.println("任务总数: " + stats.getTaskCount());
        System.out.println("已完成任务数: " + stats.getCompletedTaskCount());
    }
}
```

## 配置说明

### 基础配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `dynamictp.core.enabled` | Boolean | true | 是否启用动态线程池 |
| `dynamictp.core.enabled-banner` | Boolean | true | 是否打印启动横幅 |
| `dynamictp.core.env` | String | - | 应用环境标识 |

### 线程池配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `thread-pool-name` | String | - | 线程池名称（必填） |
| `core-pool-size` | Integer | 1 | 核心线程数 |
| `maximum-pool-size` | Integer | CPU核心数 | 最大线程数 |
| `keep-alive-time` | Long | 60 | 线程存活时间 |
| `unit` | String | SECONDS | 时间单位 |
| `queue-type` | String | VariableLinkedBlockingQueue | 队列类型 |
| `queue-capacity` | Integer | 1024 | 队列容量 |
| `rejected-handler-type` | String | CallerRunsPolicy | 拒绝策略 |
| `thread-name-prefix` | String | - | 线程名前缀 |
| `allow-core-thread-time-out` | Boolean | false | 是否允许核心线程超时 |
| `pre-start-all-core-threads` | Boolean | false | 是否预启动所有核心线程 |
| `fair` | Boolean | false | 是否公平队列 |

### Nacos配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `server-addr` | String | 127.0.0.1:8848 | Nacos服务器地址 |
| `namespace` | String | "" | 命名空间 |
| `group` | String | DEFAULT_GROUP | 配置组 |
| `data-id` | String | dynamic-tp-config.yml | 配置数据ID |
| `type` | String | yaml | 配置类型 |
| `username` | String | - | 用户名 |
| `password` | String | - | 密码 |
| `connect-timeout` | Long | 3000 | 连接超时时间（毫秒） |
| `read-timeout` | Long | 5000 | 读取超时时间（毫秒） |

## API参考

### DtpCoreApi

```java
// 获取线程池执行器
DtpCoreExecutor getExecutor(String poolName)

// 获取所有线程池执行器
Map<String, DtpCoreExecutor> getAllExecutors()

// 获取线程池统计信息
ThreadPoolStats getThreadPoolStats(String poolName)

// 获取所有线程池统计信息
List<ThreadPoolStats> getAllThreadPoolStats()

// 检查线程池是否存在
boolean containsExecutor(String poolName)

// 获取注册的线程池数量
int getExecutorCount()

// 获取线程池名称列表
List<String> getExecutorNames()
```

## 动态配置更新

当启用Nacos配置中心时，可以通过修改Nacos中的配置来动态更新线程池参数，无需重启应用。

配置更新支持的参数：
- `core-pool-size`: 核心线程数
- `maximum-pool-size`: 最大线程数  
- `keep-alive-time`: 线程存活时间

## 注意事项

1. 线程池名称必须唯一
2. 核心线程数不能大于最大线程数
3. 队列容量建议根据实际业务场景设置
4. 使用Nacos配置中心时，确保Nacos服务可用
5. 应用关闭时会自动关闭所有线程池

## 示例项目

参考 `src/test/java/org/dromara/dynamictp/starter/core/example` 目录下的示例代码。

## 许可证

Apache License 2.0
