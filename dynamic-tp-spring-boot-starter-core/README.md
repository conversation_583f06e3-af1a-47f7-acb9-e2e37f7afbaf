# Dynamic ThreadPool Spring Boot Starter Core

轻量级动态线程池管理核心starter，提供核心线程池管理功能和Nacos配置中心集成。

## 特性

- 🚀 **轻量级设计**: 专注于核心线程池管理功能，无冗余依赖
- 🔧 **动态配置**: 支持运行时动态调整线程池参数
- 🌐 **Nacos集成**: 内置Nacos配置中心支持，实现配置热更新
- 📊 **统计监控**: 提供详细的线程池运行统计信息
- 🔌 **Spring Boot集成**: 无缝集成Spring Boot，开箱即用
- 🎯 **API友好**: 提供简洁易用的API接口
- 📝 **注解支持**: 支持@DynamicThreadPool注解，声明式创建线程池
- 🔄 **混合配置**: 注解方式与配置文件方式可同时使用，灵活配置

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>org.dromara.dynamictp</groupId>
    <artifactId>dynamic-tp-spring-boot-starter-core</artifactId>
    <version>2.0.0</version>
</dependency>
```

### 2. 配置线程池

在 `application.yml` 中配置线程池：

```yaml
dynamictp:
  core:
    enabled: true
    enabled-banner: true
    env: dev
    
    # 线程池配置
    executors:
      - thread-pool-name: demoExecutor
        core-pool-size: 2
        maximum-pool-size: 4
        keep-alive-time: 60
        unit: SECONDS
        queue-type: LinkedBlockingQueue
        queue-capacity: 100
        rejected-handler-type: CallerRunsPolicy
        thread-name-prefix: demo-pool
        
    # Nacos配置中心（可选）
    nacos:
      server-addr: 127.0.0.1:8848
      namespace: ""
      group: DEFAULT_GROUP
      data-id: dynamic-tp-config.yml
      type: yaml
```

### 3. 使用线程池

#### 方式一：配置文件方式

```java
import org.dromara.dynamictp.starter.core.api.DtpCoreApi;
import org.dromara.dynamictp.starter.core.executor.DtpCoreExecutor;

@Service
public class BusinessService {

    public void executeAsyncTask() {
        // 获取线程池
        DtpCoreExecutor executor = DtpCoreApi.getExecutor("demoExecutor");

        // 提交任务
        executor.execute(() -> {
            // 业务逻辑
            System.out.println("异步任务执行中...");
        });
    }

    public void getThreadPoolStats() {
        // 获取线程池统计信息
        DtpCoreExecutor.ThreadPoolStats stats = DtpCoreApi.getThreadPoolStats("demoExecutor");
        System.out.println("活跃线程数: " + stats.getActiveCount());
        System.out.println("任务总数: " + stats.getTaskCount());
        System.out.println("已完成任务数: " + stats.getCompletedTaskCount());
    }
}
```

#### 方式二：注解方式

```java
import org.dromara.dynamictp.starter.core.annotation.DynamicThreadPool;
import org.dromara.dynamictp.starter.core.annotation.EnableDynamicThreadPool;

// 1. 启用注解扫描
@SpringBootApplication
@EnableDynamicThreadPool
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}

// 2. 使用注解创建线程池
import org.dromara.dynamictp.starter.core.annotation.DynamicThreadPool;
import org.dromara.dynamictp.starter.core.enums.QueueType;
import org.dromara.dynamictp.starter.core.enums.RejectedExecutionHandlerType;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
public class AnnotationService {

    // 方法级别注解
    @DynamicThreadPool(
        threadPoolName = "annotationPool",
        corePoolSize = 2,
        maximumPoolSize = 4,
        timeUnit = TimeUnit.SECONDS,
        queueType = QueueType.LINKED_BLOCKING_QUEUE,
        queueCapacity = 100,
        rejectedHandlerType = RejectedExecutionHandlerType.CALLER_RUNS_POLICY,
        description = "注解创建的线程池"
    )
    public void createThreadPool() {
        // 此方法触发线程池创建
    }

    public void executeTask() {
        DtpCoreExecutor executor = DtpCoreApi.getExecutor("annotationPool");
        executor.execute(() -> {
            System.out.println("注解线程池任务执行中...");
        });
    }
}

// 3. 类级别注解
@Component
@DynamicThreadPool(
    threadPoolName = "classLevelPool",
    corePoolSize = 3,
    maximumPoolSize = 6,
    timeUnit = TimeUnit.SECONDS,
    queueType = QueueType.ARRAY_BLOCKING_QUEUE,
    rejectedHandlerType = RejectedExecutionHandlerType.ABORT_POLICY,
    threadNamePrefix = "class-pool"
)
public class ClassLevelService {
    // 类实例化时自动创建线程池
}
```

## 配置说明

### 基础配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `dynamictp.core.enabled` | Boolean | true | 是否启用动态线程池 |
| `dynamictp.core.enabled-banner` | Boolean | true | 是否打印启动横幅 |
| `dynamictp.core.env` | String | - | 应用环境标识 |

### 线程池配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `thread-pool-name` | String | - | 线程池名称（必填） |
| `core-pool-size` | Integer | 1 | 核心线程数 |
| `maximum-pool-size` | Integer | CPU核心数 | 最大线程数 |
| `keep-alive-time` | Long | 60 | 线程存活时间 |
| `unit` | String | SECONDS | 时间单位 |
| `queue-type` | String | VariableLinkedBlockingQueue | 队列类型 |
| `queue-capacity` | Integer | 1024 | 队列容量 |
| `rejected-handler-type` | String | CallerRunsPolicy | 拒绝策略 |
| `thread-name-prefix` | String | - | 线程名前缀 |
| `allow-core-thread-time-out` | Boolean | false | 是否允许核心线程超时 |
| `pre-start-all-core-threads` | Boolean | false | 是否预启动所有核心线程 |
| `fair` | Boolean | false | 是否公平队列 |

### Nacos配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `server-addr` | String | 127.0.0.1:8848 | Nacos服务器地址 |
| `namespace` | String | "" | 命名空间 |
| `group` | String | DEFAULT_GROUP | 配置组 |
| `data-id` | String | dynamic-tp-config.yml | 配置数据ID |
| `type` | String | yaml | 配置类型 |
| `username` | String | - | 用户名 |
| `password` | String | - | 密码 |
| `connect-timeout` | Long | 3000 | 连接超时时间（毫秒） |
| `read-timeout` | Long | 5000 | 读取超时时间（毫秒） |

### 注解配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `dynamictp.core.annotation.enabled` | Boolean | true | 是否启用注解扫描 |
| `dynamictp.core.annotation.base-packages` | String[] | [] | 扫描的基础包路径 |
| `dynamictp.core.annotation.order` | Integer | 0 | 扫描优先级 |
| `dynamictp.core.annotation.enable-method-level` | Boolean | true | 是否启用方法级别注解 |
| `dynamictp.core.annotation.enable-class-level` | Boolean | true | 是否启用类级别注解 |

### @DynamicThreadPool注解参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `threadPoolName` | String | "" | 线程池名称（必须唯一） |
| `corePoolSize` | int | 1 | 核心线程数 |
| `maximumPoolSize` | int | -1 | 最大线程数（-1表示CPU核心数） |
| `keepAliveTime` | long | 60 | 线程存活时间 |
| `timeUnit` | TimeUnit | TimeUnit.SECONDS | 时间单位（枚举类型，提供类型安全） |
| `queueType` | QueueType | QueueType.LINKED_BLOCKING_QUEUE | 队列类型（枚举类型，提供类型安全） |
| `queueCapacity` | int | 1024 | 队列容量 |
| `rejectedHandlerType` | RejectedExecutionHandlerType | RejectedExecutionHandlerType.CALLER_RUNS_POLICY | 拒绝策略（枚举类型，提供类型安全） |
| `threadNamePrefix` | String | "" | 线程名前缀 |
| `allowCoreThreadTimeOut` | boolean | false | 是否允许核心线程超时 |
| `preStartAllCoreThreads` | boolean | false | 是否预启动所有核心线程 |
| `fair` | boolean | false | 是否公平队列 |
| `enableRefresh` | boolean | true | 是否启用动态配置刷新 |
| `description` | String | "" | 线程池描述信息 |

#### 枚举类型说明

**QueueType（队列类型）：**
- `LINKED_BLOCKING_QUEUE` - LinkedBlockingQueue（默认）
- `ARRAY_BLOCKING_QUEUE` - ArrayBlockingQueue
- `SYNCHRONOUS_QUEUE` - SynchronousQueue
- `PRIORITY_BLOCKING_QUEUE` - PriorityBlockingQueue
- `LINKED_TRANSFER_QUEUE` - LinkedTransferQueue
- `DELAY_QUEUE` - DelayQueue

**RejectedExecutionHandlerType（拒绝策略）：**
- `CALLER_RUNS_POLICY` - CallerRunsPolicy（默认）
- `ABORT_POLICY` - AbortPolicy
- `DISCARD_POLICY` - DiscardPolicy
- `DISCARD_OLDEST_POLICY` - DiscardOldestPolicy

**TimeUnit（时间单位）：**
- 使用标准Java `java.util.concurrent.TimeUnit` 枚举
- 支持：NANOSECONDS, MICROSECONDS, MILLISECONDS, SECONDS, MINUTES, HOURS, DAYS
| `priority` | int | 0 | 优先级 |

## API参考

### DtpCoreApi

```java
// 获取线程池执行器
DtpCoreExecutor getExecutor(String poolName)

// 获取所有线程池执行器
Map<String, DtpCoreExecutor> getAllExecutors()

// 获取线程池统计信息
ThreadPoolStats getThreadPoolStats(String poolName)

// 获取所有线程池统计信息
List<ThreadPoolStats> getAllThreadPoolStats()

// 检查线程池是否存在
boolean containsExecutor(String poolName)

// 获取注册的线程池数量
int getExecutorCount()

// 获取线程池名称列表
List<String> getExecutorNames()
```

## 使用方式对比

### 配置文件方式 vs 注解方式

| 特性 | 配置文件方式 | 注解方式 |
|------|-------------|----------|
| **配置位置** | application.yml | 代码中的注解 |
| **配置集中性** | 集中配置 | 分散在代码中 |
| **类型安全** | 运行时检查 | 编译时检查 |
| **IDE支持** | 配置提示 | 注解提示 |
| **动态修改** | 支持热更新 | 需要重新部署 |
| **适用场景** | 运维配置、环境差异 | 开发阶段、固定配置 |

### 混合使用示例

```yaml
# application.yml - 配置文件方式
dynamictp:
  core:
    enabled: true
    annotation:
      enabled: true  # 启用注解扫描
    executors:
      - thread-pool-name: configPool
        core-pool-size: 2
        maximum-pool-size: 4
```

```java
// 注解方式与配置文件方式同时使用
@Service
public class MixedUsageService {

    // 注解方式创建
    @DynamicThreadPool(
        threadPoolName = "annotationPool",
        corePoolSize = 1,
        maximumPoolSize = 2
    )
    public void createAnnotationPool() {}

    public void useBothPools() {
        // 使用配置文件创建的线程池
        DtpCoreExecutor configPool = DtpCoreApi.getExecutor("configPool");

        // 使用注解创建的线程池
        DtpCoreExecutor annotationPool = DtpCoreApi.getExecutor("annotationPool");

        // 两种方式创建的线程池可以同时使用
        configPool.execute(() -> System.out.println("Config pool task"));
        annotationPool.execute(() -> System.out.println("Annotation pool task"));
    }
}
```

## 动态配置更新

当启用Nacos配置中心时，可以通过修改Nacos中的配置来动态更新线程池参数，无需重启应用。

配置更新支持的参数：
- `core-pool-size`: 核心线程数
- `maximum-pool-size`: 最大线程数
- `keep-alive-time`: 线程存活时间

**注意**: 注解方式创建的线程池也支持Nacos动态配置更新。

## 注意事项

1. 线程池名称必须唯一
2. 核心线程数不能大于最大线程数
3. 队列容量建议根据实际业务场景设置
4. 使用Nacos配置中心时，确保Nacos服务可用
5. 应用关闭时会自动关闭所有线程池

## 示例项目

参考 `src/test/java/org/dromara/dynamictp/starter/core/example` 目录下的示例代码。

## 许可证

Apache License 2.0
