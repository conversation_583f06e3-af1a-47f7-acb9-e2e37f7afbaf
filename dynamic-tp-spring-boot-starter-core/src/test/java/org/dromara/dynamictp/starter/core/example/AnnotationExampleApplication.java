/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.example;

import lombok.extern.slf4j.Slf4j;
import org.dromara.dynamictp.starter.core.annotation.EnableDynamicThreadPool;
import org.dromara.dynamictp.starter.core.api.DtpCoreApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 注解示例应用
 * 
 * 演示如何使用@DynamicThreadPool注解和@EnableDynamicThreadPool注解
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@SpringBootApplication
@EnableDynamicThreadPool(
        basePackages = {"org.dromara.dynamictp.starter.core.example"},
        enabled = true,
        order = 0
)
public class AnnotationExampleApplication implements CommandLineRunner {

    @Autowired
    private AnnotationExampleService annotationExampleService;

    @Autowired
    private ClassLevelAnnotationExample classLevelAnnotationExample;

    public static void main(String[] args) {
        SpringApplication.run(AnnotationExampleApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("=== Dynamic ThreadPool Annotation Example Started ===");

        // 等待所有线程池创建完成
        Thread.sleep(2000);

        // 显示所有注册的线程池
        showRegisteredThreadPools();

        // 演示业务使用
        demonstrateBusinessUsage();

        // 演示混合使用（注解 + 配置文件）
        demonstrateMixedUsage();

        log.info("=== Dynamic ThreadPool Annotation Example Completed ===");
    }

    /**
     * 显示所有注册的线程池
     */
    private void showRegisteredThreadPools() {
        log.info("=== Registered Thread Pools ===");
        log.info("Total thread pools: {}", DtpCoreApi.getExecutorCount());
        
        DtpCoreApi.getExecutorNames().forEach(name -> {
            log.info("Thread pool: {}", name);
        });

        log.info("=== Thread Pool Statistics ===");
        DtpCoreApi.getAllThreadPoolStats().forEach(stats -> {
            log.info("Pool: {}, Core: {}, Max: {}, Active: {}, Queue: {}", 
                    stats.getThreadPoolName(),
                    stats.getCorePoolSize(),
                    stats.getMaximumPoolSize(),
                    stats.getActiveCount(),
                    stats.getQueueSize());
        });
    }

    /**
     * 演示业务使用
     */
    private void demonstrateBusinessUsage() throws InterruptedException {
        log.info("=== Demonstrating Business Usage ===");

        // 使用注解服务处理业务任务
        annotationExampleService.processBusinessTask("Task-001");
        annotationExampleService.processBusinessTask("Task-002");
        annotationExampleService.processBusinessTask("Task-003");

        // 使用类级别注解处理业务逻辑
        classLevelAnnotationExample.executeBusinessLogic("Data-001");
        classLevelAnnotationExample.executeBusinessLogic("Data-002");

        // 批量任务处理
        String[] batchTasks = {"Batch-001", "Batch-002", "Batch-003", "Batch-004", "Batch-005"};
        annotationExampleService.processBatchTasks(batchTasks);

        // 等待任务完成
        Thread.sleep(3000);

        // 再次显示统计信息
        log.info("=== Final Statistics ===");
        DtpCoreApi.getAllThreadPoolStats().forEach(stats -> {
            log.info("Pool: {}, Executed: {}, Completed: {}, Rejected: {}", 
                    stats.getThreadPoolName(),
                    stats.getExecuteCount(),
                    stats.getCompletedTaskCount(),
                    stats.getRejectCount());
        });
    }

    /**
     * 演示混合使用（注解 + 配置文件）
     */
    private void demonstrateMixedUsage() {
        log.info("=== Demonstrating Mixed Usage (Annotation + Configuration) ===");

        // 检查是否有配置文件创建的线程池
        if (DtpCoreApi.containsExecutor("demoExecutor")) {
            log.info("Configuration-based thread pool 'demoExecutor' found");
            var executor = DtpCoreApi.getExecutor("demoExecutor");
            executor.execute(() -> {
                log.info("Task executed in configuration-based pool, thread: {}", 
                        Thread.currentThread().getName());
            });
        } else {
            log.info("Configuration-based thread pool 'demoExecutor' not found");
        }

        if (DtpCoreApi.containsExecutor("asyncExecutor")) {
            log.info("Configuration-based thread pool 'asyncExecutor' found");
            var executor = DtpCoreApi.getExecutor("asyncExecutor");
            executor.execute(() -> {
                log.info("Task executed in async configuration pool, thread: {}", 
                        Thread.currentThread().getName());
            });
        } else {
            log.info("Configuration-based thread pool 'asyncExecutor' not found");
        }

        // 显示所有线程池的来源信息
        log.info("=== Thread Pool Sources ===");
        DtpCoreApi.getExecutorNames().forEach(name -> {
            var executor = DtpCoreApi.getExecutor(name);
            log.info("Pool: {}, Type: {}", name, 
                    name.contains("method") || name.contains("class") ? "Annotation-based" : "Configuration-based");
        });
    }
}
