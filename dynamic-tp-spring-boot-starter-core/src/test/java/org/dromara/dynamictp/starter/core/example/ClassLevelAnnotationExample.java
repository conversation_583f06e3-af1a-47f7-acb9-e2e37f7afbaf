/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.example;

import lombok.extern.slf4j.Slf4j;
import org.dromara.dynamictp.starter.core.annotation.DynamicThreadPool;
import org.dromara.dynamictp.starter.core.api.DtpCoreApi;
import org.dromara.dynamictp.starter.core.executor.DtpCoreExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * 类级别注解示例
 * 
 * 演示如何在类上使用@DynamicThreadPool注解
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@Component
@DynamicThreadPool(
        threadPoolName = "classLevelPool",
        corePoolSize = 2,
        maximumPoolSize = 5,
        keepAliveTime = 90,
        timeUnit = "SECONDS",
        queueType = "LinkedBlockingQueue",
        queueCapacity = 150,
        rejectedHandlerType = "CallerRunsPolicy",
        threadNamePrefix = "class-level",
        allowCoreThreadTimeOut = false,
        preStartAllCoreThreads = true,
        fair = false,
        priority = 0,
        enableRefresh = true,
        description = "类级别线程池示例，用于处理通用任务"
)
public class ClassLevelAnnotationExample {

    @PostConstruct
    public void init() {
        log.info("ClassLevelAnnotationExample initialized");
        demonstrateClassLevelPool();
    }

    /**
     * 演示类级别线程池使用
     */
    private void demonstrateClassLevelPool() {
        // 等待线程池创建完成
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return;
        }

        DtpCoreExecutor executor = DtpCoreApi.getExecutor("classLevelPool");
        if (executor != null) {
            log.info("Class level pool found: {}", executor.getThreadPoolName());
            
            // 提交多个任务
            for (int i = 0; i < 8; i++) {
                final int taskId = i;
                executor.execute(() -> {
                    log.info("Class level task {} executed, thread: {}", 
                            taskId, Thread.currentThread().getName());
                    try {
                        TimeUnit.MILLISECONDS.sleep(300);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    log.info("Class level task {} completed", taskId);
                });
            }

            // 打印统计信息
            try {
                Thread.sleep(1000);
                DtpCoreExecutor.ThreadPoolStats stats = executor.getStats();
                log.info("Class level pool stats - Active: {}, Completed: {}, Total: {}, Queue: {}",
                        stats.getActiveCount(),
                        stats.getCompletedTaskCount(),
                        stats.getTaskCount(),
                        stats.getQueueSize());
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        } else {
            log.warn("Class level pool not found");
        }
    }

    /**
     * 业务方法，使用类级别创建的线程池
     */
    public void executeBusinessLogic(String data) {
        DtpCoreExecutor executor = DtpCoreApi.getExecutor("classLevelPool");
        if (executor != null) {
            executor.execute(() -> {
                log.info("Executing business logic with data: {}, thread: {}", 
                        data, Thread.currentThread().getName());
                // 模拟业务处理
                try {
                    TimeUnit.MILLISECONDS.sleep(200);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                log.info("Business logic completed for data: {}", data);
            });
        }
    }
}
