/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.example;

import lombok.extern.slf4j.Slf4j;
import org.dromara.dynamictp.starter.core.api.DtpCoreApi;
import org.dromara.dynamictp.starter.core.executor.DtpCoreExecutor;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.util.concurrent.TimeUnit;

/**
 * 动态线程池核心示例应用
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@SpringBootApplication
public class DtpCoreExampleApplication implements CommandLineRunner {

    public static void main(String[] args) {
        SpringApplication.run(DtpCoreExampleApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("=== Dynamic ThreadPool Core Example Started ===");

        // 等待自动配置完成
        Thread.sleep(2000);

        // 演示获取线程池
        demonstrateGetExecutor();

        // 演示提交任务
        demonstrateSubmitTasks();

        // 演示获取统计信息
        demonstrateGetStats();

        log.info("=== Dynamic ThreadPool Core Example Completed ===");
    }

    /**
     * 演示获取线程池
     */
    private void demonstrateGetExecutor() {
        log.info("--- Demonstrate Get Executor ---");

        // 获取所有线程池名称
        log.info("All executor names: {}", DtpCoreApi.getExecutorNames());

        // 获取线程池数量
        log.info("Total executor count: {}", DtpCoreApi.getExecutorCount());

        // 检查特定线程池是否存在
        boolean exists = DtpCoreApi.containsExecutor("demoExecutor");
        log.info("Executor 'demoExecutor' exists: {}", exists);

        if (exists) {
            DtpCoreExecutor executor = DtpCoreApi.getExecutor("demoExecutor");
            log.info("Got executor: {}, corePoolSize: {}, maximumPoolSize: {}",
                    executor.getThreadPoolName(), executor.getCorePoolSize(), executor.getMaximumPoolSize());
        }
    }

    /**
     * 演示提交任务
     */
    private void demonstrateSubmitTasks() {
        log.info("--- Demonstrate Submit Tasks ---");

        DtpCoreExecutor executor = DtpCoreApi.getExecutor("demoExecutor");
        if (executor == null) {
            log.warn("Executor 'demoExecutor' not found, skipping task submission");
            return;
        }

        // 提交一些任务
        for (int i = 0; i < 10; i++) {
            final int taskId = i;
            executor.execute(() -> {
                try {
                    log.info("Executing task {}", taskId);
                    Thread.sleep(1000); // 模拟任务执行
                    log.info("Task {} completed", taskId);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("Task {} interrupted", taskId);
                }
            });
        }

        log.info("Submitted 10 tasks to executor 'demoExecutor'");
    }

    /**
     * 演示获取统计信息
     */
    private void demonstrateGetStats() throws InterruptedException {
        log.info("--- Demonstrate Get Stats ---");

        // 等待任务执行
        Thread.sleep(3000);

        // 获取特定线程池统计信息
        DtpCoreExecutor.ThreadPoolStats stats = DtpCoreApi.getThreadPoolStats("demoExecutor");
        if (stats != null) {
            log.info("Executor 'demoExecutor' stats:");
            log.info("  - Active Count: {}", stats.getActiveCount());
            log.info("  - Pool Size: {}", stats.getPoolSize());
            log.info("  - Task Count: {}", stats.getTaskCount());
            log.info("  - Completed Task Count: {}", stats.getCompletedTaskCount());
            log.info("  - Execute Count: {}", stats.getExecuteCount());
            log.info("  - Reject Count: {}", stats.getRejectCount());
            log.info("  - Queue Size: {}", stats.getQueueSize());
        }

        // 获取所有线程池统计信息
        log.info("All thread pool stats:");
        DtpCoreApi.getAllThreadPoolStats().forEach(stat -> {
            log.info("  - {}: active={}, poolSize={}, taskCount={}, completedTaskCount={}",
                    stat.getThreadPoolName(), stat.getActiveCount(), stat.getPoolSize(),
                    stat.getTaskCount(), stat.getCompletedTaskCount());
        });
    }
}
