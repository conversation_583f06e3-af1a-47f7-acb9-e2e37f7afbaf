/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core;

import org.dromara.dynamictp.starter.core.api.DtpCoreApi;
import org.dromara.dynamictp.starter.core.builder.DtpCoreExecutorBuilder;
import org.dromara.dynamictp.starter.core.executor.DtpCoreExecutor;
import org.dromara.dynamictp.starter.core.properties.DtpCoreProperties;
import org.dromara.dynamictp.starter.core.registry.DtpCoreRegistry;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;

/**
 * 动态线程池核心功能测试
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public class DtpCoreTest {

    private static final String TEST_POOL_NAME = "testPool";

    @Before
    public void setUp() {
        // 清理注册表
        DtpCoreRegistry.removeExecutor(TEST_POOL_NAME);
    }

    @After
    public void tearDown() {
        // 清理注册表
        DtpCoreRegistry.removeExecutor(TEST_POOL_NAME);
    }

    @Test
    public void testExecutorBuilder() {
        // 测试构建器
        DtpCoreExecutor executor = DtpCoreExecutorBuilder.newBuilder()
                .threadPoolName(TEST_POOL_NAME)
                .corePoolSize(2)
                .maximumPoolSize(4)
                .keepAliveTime(60)
                .timeUnit(TimeUnit.SECONDS)
                .build();

        assertNotNull(executor);
        assertEquals(TEST_POOL_NAME, executor.getThreadPoolName());
        assertEquals(2, executor.getCorePoolSize());
        assertEquals(4, executor.getMaximumPoolSize());
        assertEquals(60, executor.getKeepAliveTime(TimeUnit.SECONDS));
    }

    @Test
    public void testExecutorRegistry() {
        // 创建线程池
        DtpCoreExecutor executor = DtpCoreExecutorBuilder.newBuilder()
                .threadPoolName(TEST_POOL_NAME)
                .corePoolSize(1)
                .maximumPoolSize(2)
                .build();

        // 测试注册
        DtpCoreRegistry.registerExecutor(executor);
        assertTrue(DtpCoreRegistry.containsExecutor(TEST_POOL_NAME));
        assertEquals(executor, DtpCoreRegistry.getExecutor(TEST_POOL_NAME));

        // 测试移除
        DtpCoreExecutor removed = DtpCoreRegistry.removeExecutor(TEST_POOL_NAME);
        assertEquals(executor, removed);
        assertFalse(DtpCoreRegistry.containsExecutor(TEST_POOL_NAME));
    }

    @Test
    public void testExecutorExecution() throws InterruptedException {
        // 创建并注册线程池
        DtpCoreExecutor executor = DtpCoreExecutorBuilder.newBuilder()
                .threadPoolName(TEST_POOL_NAME)
                .corePoolSize(2)
                .maximumPoolSize(4)
                .buildAndRegister();

        // 测试任务执行
        CountDownLatch latch = new CountDownLatch(5);
        for (int i = 0; i < 5; i++) {
            executor.execute(() -> {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待任务完成
        assertTrue(latch.await(5, TimeUnit.SECONDS));

        // 验证统计信息
        DtpCoreExecutor.ThreadPoolStats stats = executor.getStats();
        assertNotNull(stats);
        assertEquals(TEST_POOL_NAME, stats.getThreadPoolName());
        assertTrue(stats.getExecuteCount() >= 5);
    }

    @Test
    public void testExecutorRefresh() {
        // 创建并注册线程池
        DtpCoreExecutor executor = DtpCoreExecutorBuilder.newBuilder()
                .threadPoolName(TEST_POOL_NAME)
                .corePoolSize(1)
                .maximumPoolSize(2)
                .buildAndRegister();

        // 测试刷新配置
        executor.refresh(3, 6, 120, TimeUnit.SECONDS);

        assertEquals(3, executor.getCorePoolSize());
        assertEquals(6, executor.getMaximumPoolSize());
        assertEquals(120, executor.getKeepAliveTime(TimeUnit.SECONDS));
    }

    @Test
    public void testDtpCoreApi() {
        // 创建并注册线程池
        DtpCoreExecutorBuilder.newBuilder()
                .threadPoolName(TEST_POOL_NAME)
                .corePoolSize(1)
                .maximumPoolSize(2)
                .buildAndRegister();

        // 测试API
        assertTrue(DtpCoreApi.containsExecutor(TEST_POOL_NAME));
        assertNotNull(DtpCoreApi.getExecutor(TEST_POOL_NAME));
        assertTrue(DtpCoreApi.getExecutorCount() > 0);
        assertTrue(DtpCoreApi.getExecutorNames().contains(TEST_POOL_NAME));

        DtpCoreExecutor.ThreadPoolStats stats = DtpCoreApi.getThreadPoolStats(TEST_POOL_NAME);
        assertNotNull(stats);
        assertEquals(TEST_POOL_NAME, stats.getThreadPoolName());
    }

    @Test
    public void testBuildFromProperties() {
        // 创建配置属性
        DtpCoreProperties.ThreadPoolProperties props = new DtpCoreProperties.ThreadPoolProperties();
        props.setThreadPoolName(TEST_POOL_NAME);
        props.setCorePoolSize(2);
        props.setMaximumPoolSize(4);
        props.setKeepAliveTime(60L);
        props.setUnit("SECONDS");
        props.setQueueType("LinkedBlockingQueue");
        props.setQueueCapacity(100);
        props.setRejectedHandlerType("CallerRunsPolicy");
        props.setThreadNamePrefix("test-pool");

        // 从配置构建线程池
        DtpCoreExecutor executor = DtpCoreExecutorBuilder.buildFromProperties(props);

        assertNotNull(executor);
        assertEquals(TEST_POOL_NAME, executor.getThreadPoolName());
        assertEquals(2, executor.getCorePoolSize());
        assertEquals(4, executor.getMaximumPoolSize());
        assertEquals(60, executor.getKeepAliveTime(TimeUnit.SECONDS));
    }
}
