/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core;

import org.dromara.dynamictp.starter.core.annotation.DynamicThreadPool;
import org.dromara.dynamictp.starter.core.api.DtpCoreApi;
import org.dromara.dynamictp.starter.core.executor.DtpCoreExecutor;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.stereotype.Component;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;

/**
 * 混合使用集成测试
 * 
 * 测试注解方式和配置文件方式的混合使用场景
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public class MixedUsageIntegrationTest {

    @Before
    public void setUp() {
        // 清理环境
        clearAllExecutors();
    }

    @After
    public void tearDown() {
        // 清理环境
        clearAllExecutors();
    }

    private void clearAllExecutors() {
        DtpCoreApi.getExecutorNames().forEach(name -> {
            try {
                DtpCoreApi.getExecutor(name).shutdown();
            } catch (Exception e) {
                // 忽略关闭异常
            }
        });
    }

    @Test
    public void testAnnotationAndConfigurationCoexistence() {
        // 模拟配置文件创建的线程池
        createConfigurationBasedPool();
        
        // 模拟注解创建的线程池
        createAnnotationBasedPool();

        // 验证两种方式创建的线程池都存在
        assertTrue("Configuration-based pool should exist", 
                DtpCoreApi.containsExecutor("configPool"));
        assertTrue("Annotation-based pool should exist", 
                DtpCoreApi.containsExecutor("annotationPool"));

        // 验证线程池数量
        assertTrue("Should have at least 2 pools", DtpCoreApi.getExecutorCount() >= 2);

        // 验证线程池配置正确
        DtpCoreExecutor configPool = DtpCoreApi.getExecutor("configPool");
        DtpCoreExecutor annotationPool = DtpCoreApi.getExecutor("annotationPool");

        assertNotNull("Config pool should not be null", configPool);
        assertNotNull("Annotation pool should not be null", annotationPool);

        assertEquals("Config pool core size", 2, configPool.getCorePoolSize());
        assertEquals("Annotation pool core size", 3, annotationPool.getCorePoolSize());
    }

    @Test
    public void testBothPoolTypesExecuteTasks() throws InterruptedException {
        // 创建两种类型的线程池
        createConfigurationBasedPool();
        createAnnotationBasedPool();

        CountDownLatch configLatch = new CountDownLatch(2);
        CountDownLatch annotationLatch = new CountDownLatch(3);

        // 在配置文件创建的线程池中执行任务
        DtpCoreExecutor configPool = DtpCoreApi.getExecutor("configPool");
        for (int i = 0; i < 2; i++) {
            configPool.execute(() -> {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    configLatch.countDown();
                }
            });
        }

        // 在注解创建的线程池中执行任务
        DtpCoreExecutor annotationPool = DtpCoreApi.getExecutor("annotationPool");
        for (int i = 0; i < 3; i++) {
            annotationPool.execute(() -> {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    annotationLatch.countDown();
                }
            });
        }

        // 等待所有任务完成
        assertTrue("Config pool tasks should complete", 
                configLatch.await(5, TimeUnit.SECONDS));
        assertTrue("Annotation pool tasks should complete", 
                annotationLatch.await(5, TimeUnit.SECONDS));

        // 验证统计信息
        DtpCoreExecutor.ThreadPoolStats configStats = configPool.getStats();
        DtpCoreExecutor.ThreadPoolStats annotationStats = annotationPool.getStats();

        assertTrue("Config pool should have executed tasks", 
                configStats.getExecuteCount() >= 2);
        assertTrue("Annotation pool should have executed tasks", 
                annotationStats.getExecuteCount() >= 3);
    }

    @Test
    public void testStatisticsAggregation() {
        // 创建多个线程池
        createConfigurationBasedPool();
        createAnnotationBasedPool();

        // 获取所有统计信息
        java.util.List<DtpCoreExecutor.ThreadPoolStats> allStats = DtpCoreApi.getAllThreadPoolStats();
        
        assertTrue("Should have statistics for multiple pools", allStats.size() >= 2);

        // 验证统计信息包含正确的线程池
        boolean hasConfigPool = allStats.stream()
                .anyMatch(stats -> "configPool".equals(stats.getThreadPoolName()));
        boolean hasAnnotationPool = allStats.stream()
                .anyMatch(stats -> "annotationPool".equals(stats.getThreadPoolName()));

        assertTrue("Should have config pool statistics", hasConfigPool);
        assertTrue("Should have annotation pool statistics", hasAnnotationPool);
    }

    @Test
    public void testDynamicRefreshCompatibility() {
        // 创建两种类型的线程池
        createConfigurationBasedPool();
        createAnnotationBasedPool();

        DtpCoreExecutor configPool = DtpCoreApi.getExecutor("configPool");
        DtpCoreExecutor annotationPool = DtpCoreApi.getExecutor("annotationPool");

        // 测试动态刷新功能
        configPool.refresh(4, 8, 120, TimeUnit.SECONDS);
        annotationPool.refresh(5, 10, 180, TimeUnit.SECONDS);

        // 验证刷新后的配置
        assertEquals("Config pool core size after refresh", 4, configPool.getCorePoolSize());
        assertEquals("Config pool max size after refresh", 8, configPool.getMaximumPoolSize());
        assertEquals("Annotation pool core size after refresh", 5, annotationPool.getCorePoolSize());
        assertEquals("Annotation pool max size after refresh", 10, annotationPool.getMaximumPoolSize());
    }

    @Test
    public void testThreadPoolNamingIsolation() {
        // 确保不同方式创建的线程池名称不会冲突
        createConfigurationBasedPool();
        createAnnotationBasedPool();

        // 尝试创建同名线程池（应该被处理或忽略）
        try {
            createDuplicateNamePool();
            
            // 验证原有线程池仍然存在且配置正确
            DtpCoreExecutor configPool = DtpCoreApi.getExecutor("configPool");
            assertNotNull("Original config pool should still exist", configPool);
            assertEquals("Original config pool should maintain its configuration", 
                    2, configPool.getCorePoolSize());
        } catch (Exception e) {
            // 如果抛出异常，这也是可接受的行为
            assertTrue("Exception handling for duplicate names is acceptable", true);
        }
    }

    /**
     * 模拟配置文件方式创建线程池
     */
    private void createConfigurationBasedPool() {
        // 这里应该通过配置文件方式创建，为了测试简化，使用Builder模拟
        DtpCoreExecutor executor = org.dromara.dynamictp.starter.core.builder.DtpCoreExecutorBuilder.newBuilder()
                .threadPoolName("configPool")
                .corePoolSize(2)
                .maximumPoolSize(4)
                .keepAliveTime(60)
                .timeUnit(TimeUnit.SECONDS)
                .buildAndRegister();
    }

    /**
     * 模拟注解方式创建线程池
     */
    private void createAnnotationBasedPool() {
        // 这里应该通过注解方式创建，为了测试简化，使用Builder模拟
        DtpCoreExecutor executor = org.dromara.dynamictp.starter.core.builder.DtpCoreExecutorBuilder.newBuilder()
                .threadPoolName("annotationPool")
                .corePoolSize(3)
                .maximumPoolSize(6)
                .keepAliveTime(90)
                .timeUnit(TimeUnit.SECONDS)
                .buildAndRegister();
    }

    /**
     * 尝试创建重复名称的线程池
     */
    private void createDuplicateNamePool() {
        DtpCoreExecutor executor = org.dromara.dynamictp.starter.core.builder.DtpCoreExecutorBuilder.newBuilder()
                .threadPoolName("configPool") // 重复名称
                .corePoolSize(1)
                .maximumPoolSize(2)
                .buildAndRegister();
    }

    /**
     * 测试用的注解组件
     */
    @Component
    @DynamicThreadPool(
            threadPoolName = "testMixedPool",
            corePoolSize = 1,
            maximumPoolSize = 3,
            description = "混合使用测试线程池"
    )
    public static class TestAnnotationComponent {
        // 测试组件
    }
}
