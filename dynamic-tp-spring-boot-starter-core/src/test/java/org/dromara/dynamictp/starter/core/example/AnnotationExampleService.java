/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.example;

import lombok.extern.slf4j.Slf4j;
import org.dromara.dynamictp.starter.core.annotation.DynamicThreadPool;
import org.dromara.dynamictp.starter.core.api.DtpCoreApi;
import org.dromara.dynamictp.starter.core.enums.QueueType;
import org.dromara.dynamictp.starter.core.enums.RejectedExecutionHandlerType;
import org.dromara.dynamictp.starter.core.executor.DtpCoreExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 注解使用示例服务
 * 
 * 演示如何使用@DynamicThreadPool注解创建和管理动态线程池
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@Service
public class AnnotationExampleService {

    /**
     * 方法级别注解示例 - 基础配置
     */
    @DynamicThreadPool(
            threadPoolName = "methodBasicPool",
            corePoolSize = 2,
            maximumPoolSize = 4,
            keepAliveTime = 60,
            timeUnit = TimeUnit.SECONDS,
            queueCapacity = 100,
            description = "方法级别基础线程池示例"
    )
    public void createMethodBasicPool() {
        log.info("Method basic pool configuration created");
    }

    /**
     * 方法级别注解示例 - 高级配置
     */
    @DynamicThreadPool(
            threadPoolName = "methodAdvancedPool",
            corePoolSize = 1,
            maximumPoolSize = 3,
            keepAliveTime = 30,
            timeUnit = TimeUnit.SECONDS,
            queueType = QueueType.ARRAY_BLOCKING_QUEUE,
            queueCapacity = 50,
            rejectedHandlerType = RejectedExecutionHandlerType.ABORT_POLICY,
            threadNamePrefix = "advanced-pool",
            allowCoreThreadTimeOut = true,
            preStartAllCoreThreads = false,
            fair = true,
            priority = 1,
            description = "方法级别高级线程池示例"
    )
    public void createMethodAdvancedPool() {
        log.info("Method advanced pool configuration created");
    }

    /**
     * 方法级别注解示例 - 异步任务处理
     */
    @DynamicThreadPool(
            threadPoolName = "asyncTaskPool",
            corePoolSize = 3,
            maximumPoolSize = 6,
            keepAliveTime = 120,
            timeUnit = TimeUnit.SECONDS,
            queueType = QueueType.LINKED_BLOCKING_QUEUE,
            queueCapacity = 200,
            rejectedHandlerType = RejectedExecutionHandlerType.CALLER_RUNS_POLICY,
            threadNamePrefix = "async-task",
            description = "异步任务处理线程池"
    )
    public void createAsyncTaskPool() {
        log.info("Async task pool configuration created");
    }

    @PostConstruct
    public void init() {
        // 触发注解处理
        createMethodBasicPool();
        createMethodAdvancedPool();
        createAsyncTaskPool();
        
        // 演示线程池使用
        demonstrateThreadPoolUsage();
    }

    /**
     * 演示线程池使用
     */
    private void demonstrateThreadPoolUsage() {
        // 等待一段时间确保线程池创建完成
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return;
        }

        // 使用基础线程池
        DtpCoreExecutor basicPool = DtpCoreApi.getExecutor("methodBasicPool");
        if (basicPool != null) {
            basicPool.execute(() -> {
                log.info("Task executed in methodBasicPool, thread: {}", Thread.currentThread().getName());
                try {
                    TimeUnit.MILLISECONDS.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }

        // 使用高级线程池
        DtpCoreExecutor advancedPool = DtpCoreApi.getExecutor("methodAdvancedPool");
        if (advancedPool != null) {
            CompletableFuture.runAsync(() -> {
                log.info("Async task executed in methodAdvancedPool, thread: {}", Thread.currentThread().getName());
                try {
                    TimeUnit.MILLISECONDS.sleep(300);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }, advancedPool);
        }

        // 使用异步任务线程池
        DtpCoreExecutor asyncPool = DtpCoreApi.getExecutor("asyncTaskPool");
        if (asyncPool != null) {
            for (int i = 0; i < 5; i++) {
                final int taskId = i;
                asyncPool.execute(() -> {
                    log.info("Async task {} executed in asyncTaskPool, thread: {}", 
                            taskId, Thread.currentThread().getName());
                    try {
                        TimeUnit.MILLISECONDS.sleep(200);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                });
            }
        }

        // 打印线程池统计信息
        printThreadPoolStats();
    }

    /**
     * 打印线程池统计信息
     */
    private void printThreadPoolStats() {
        try {
            // 等待任务执行
            Thread.sleep(2000);
            
            log.info("=== Thread Pool Statistics ===");
            DtpCoreApi.getAllThreadPoolStats().forEach(stats -> {
                log.info("Pool: {}, Active: {}, Completed: {}, Total: {}, Queue: {}", 
                        stats.getThreadPoolName(),
                        stats.getActiveCount(),
                        stats.getCompletedTaskCount(),
                        stats.getTaskCount(),
                        stats.getQueueSize());
            });
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 业务方法示例 - 使用注解创建的线程池
     */
    public void processBusinessTask(String taskData) {
        DtpCoreExecutor executor = DtpCoreApi.getExecutor("asyncTaskPool");
        if (executor != null) {
            executor.execute(() -> {
                log.info("Processing business task: {}, thread: {}", taskData, Thread.currentThread().getName());
                // 模拟业务处理
                try {
                    TimeUnit.MILLISECONDS.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                log.info("Business task completed: {}", taskData);
            });
        }
    }

    /**
     * 批量任务处理示例
     */
    public void processBatchTasks(String[] tasks) {
        DtpCoreExecutor executor = DtpCoreApi.getExecutor("methodBasicPool");
        if (executor != null) {
            for (String task : tasks) {
                executor.execute(() -> {
                    log.info("Processing batch task: {}, thread: {}", task, Thread.currentThread().getName());
                    try {
                        TimeUnit.MILLISECONDS.sleep(50);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                });
            }
        }
    }
}
