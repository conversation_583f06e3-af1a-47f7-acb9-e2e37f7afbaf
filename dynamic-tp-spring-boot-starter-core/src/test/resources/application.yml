# Dynamic ThreadPool Core 示例配置
dynamictp:
  core:
    enabled: true
    enabled-banner: true
    env: dev
    
    # 线程池配置
    executors:
      - thread-pool-name: demoExecutor
        core-pool-size: 2
        maximum-pool-size: 4
        keep-alive-time: 60
        unit: SECONDS
        queue-type: LinkedBlockingQueue
        queue-capacity: 100
        rejected-handler-type: CallerRunsPolicy
        thread-name-prefix: demo-pool
        allow-core-thread-time-out: false
        pre-start-all-core-threads: true
        fair: false
        
      - thread-pool-name: asyncExecutor
        core-pool-size: 1
        maximum-pool-size: 2
        keep-alive-time: 30
        unit: SECONDS
        queue-type: ArrayBlockingQueue
        queue-capacity: 50
        rejected-handler-type: AbortPolicy
        thread-name-prefix: async-pool
        allow-core-thread-time-out: true
        pre-start-all-core-threads: false
        fair: true
    
    # Nacos配置中心（可选）
    nacos:
      server-addr: 127.0.0.1:8848
      namespace: ""
      group: DEFAULT_GROUP
      data-id: dynamic-tp-config.yml
      type: yaml
      username: ""
      password: ""
      connect-timeout: 3000
      read-timeout: 5000

# Spring Boot 配置
spring:
  application:
    name: dynamic-tp-core-example

# 日志配置
logging:
  level:
    org.dromara.dynamictp: DEBUG
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
