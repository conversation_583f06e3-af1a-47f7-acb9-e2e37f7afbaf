/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.autoconfigure;

import lombok.extern.slf4j.Slf4j;
import org.dromara.dynamictp.starter.core.builder.DtpCoreExecutorBuilder;
import org.dromara.dynamictp.starter.core.executor.DtpCoreExecutor;
import org.dromara.dynamictp.starter.core.processor.DynamicThreadPoolAnnotationProcessor;
import org.dromara.dynamictp.starter.core.properties.DtpCoreProperties;
import org.dromara.dynamictp.starter.core.refresher.NacosCoreRefresher;
import org.dromara.dynamictp.starter.core.registry.DtpCoreRegistry;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * 动态线程池核心自动配置类
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(DtpCoreProperties.class)
@ConditionalOnProperty(prefix = "dynamictp.core", name = "enabled", havingValue = "true", matchIfMissing = true)
public class DtpCoreAutoConfiguration {

    private final DtpCoreProperties dtpCoreProperties;

    public DtpCoreAutoConfiguration(DtpCoreProperties dtpCoreProperties) {
        this.dtpCoreProperties = dtpCoreProperties;
    }

    @PostConstruct
    public void init() {
        printBanner();
        initThreadPools();
        log.info("DtpCoreAutoConfiguration initialized successfully");
    }

    /**
     * 打印启动横幅
     */
    private void printBanner() {
        if (!dtpCoreProperties.isEnabledBanner()) {
            return;
        }

        String banner = "\n" +
                "██████╗ ████████╗██████╗      ██████╗ ██████╗ ██████╗ ███████╗\n" +
                "██╔══██╗╚══██╔══╝██╔══██╗    ██╔════╝██╔═══██╗██╔══██╗██╔════╝\n" +
                "██║  ██║   ██║   ██████╔╝    ██║     ██║   ██║██████╔╝█████╗  \n" +
                "██║  ██║   ██║   ██╔═══╝     ██║     ██║   ██║██╔══██╗██╔══╝  \n" +
                "██████╔╝   ██║   ██║         ╚██████╗╚██████╔╝██║  ██║███████╗\n" +
                "╚═════╝    ╚═╝   ╚═╝          ╚═════╝ ╚═════╝ ╚═╝  ╚═╝╚══════╝\n" +
                "                                                               \n" +
                ":: Dynamic ThreadPool Core ::                    (v2.0.0)\n";

        System.out.println(banner);
    }

    /**
     * 初始化线程池
     */
    private void initThreadPools() {
        if (dtpCoreProperties.getExecutors() == null || dtpCoreProperties.getExecutors().isEmpty()) {
            log.info("DtpCoreAutoConfiguration no thread pool configurations found");
            return;
        }

        for (DtpCoreProperties.ThreadPoolProperties props : dtpCoreProperties.getExecutors()) {
            try {
                DtpCoreExecutor executor = DtpCoreExecutorBuilder.buildFromProperties(props);
                DtpCoreRegistry.registerExecutor(executor);
                log.info("DtpCoreAutoConfiguration initialized thread pool: {}", props.getThreadPoolName());
            } catch (Exception e) {
                log.error("DtpCoreAutoConfiguration failed to initialize thread pool: {}", 
                         props.getThreadPoolName(), e);
            }
        }

        log.info("DtpCoreAutoConfiguration initialized {} thread pools", 
                DtpCoreRegistry.getExecutorCount());
    }

    /**
     * 动态线程池注解处理器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "dynamictp.core.annotation", name = "enabled", havingValue = "true", matchIfMissing = true)
    public DynamicThreadPoolAnnotationProcessor dynamicThreadPoolAnnotationProcessor() {
        log.info("DtpCoreAutoConfiguration created DynamicThreadPoolAnnotationProcessor");
        return new DynamicThreadPoolAnnotationProcessor();
    }

    /**
     * Nacos配置刷新器
     */
    @Bean
    @ConditionalOnClass(name = "com.alibaba.nacos.api.config.ConfigService")
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "dynamictp.core.nacos", name = "serverAddr")
    public NacosCoreRefresher nacosCoreRefresher() {
        NacosCoreRefresher refresher = new NacosCoreRefresher(dtpCoreProperties);
        refresher.initialize();
        log.info("DtpCoreAutoConfiguration created NacosCoreRefresher");
        return refresher;
    }

    @PreDestroy
    public void destroy() {
        log.info("DtpCoreAutoConfiguration destroying...");
        DtpCoreRegistry.shutdown();
        log.info("DtpCoreAutoConfiguration destroyed");
    }
}
