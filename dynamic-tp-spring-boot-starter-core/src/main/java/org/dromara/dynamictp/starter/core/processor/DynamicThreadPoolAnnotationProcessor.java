/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.processor;

import lombok.extern.slf4j.Slf4j;
import org.dromara.dynamictp.starter.core.annotation.DynamicThreadPool;
import org.dromara.dynamictp.starter.core.annotation.EnableDynamicThreadPool;
import org.dromara.dynamictp.starter.core.builder.DtpCoreExecutorBuilder;
import org.dromara.dynamictp.starter.core.enums.QueueType;
import org.dromara.dynamictp.starter.core.enums.RejectedExecutionHandlerType;
import org.dromara.dynamictp.starter.core.executor.DtpCoreExecutor;
import org.dromara.dynamictp.starter.core.registry.DtpCoreRegistry;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.*;

/**
 * 动态线程池注解处理器
 * 
 * 扫描并处理@DynamicThreadPool注解，自动创建对应的线程池实例
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@Component
public class DynamicThreadPoolAnnotationProcessor implements BeanPostProcessor, ApplicationContextAware {

    private ApplicationContext applicationContext;
    private final Set<String> processedThreadPools = new HashSet<>();
    private final List<ThreadPoolDefinition> threadPoolDefinitions = new ArrayList<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        Class<?> targetClass = bean.getClass();
        
        // 检查是否启用了注解扫描
        if (!isAnnotationScanEnabled()) {
            return bean;
        }

        // 处理类级别的注解
        processClassLevelAnnotation(targetClass, beanName);
        
        // 处理方法级别的注解
        processMethodLevelAnnotations(targetClass, beanName);
        
        return bean;
    }

    /**
     * 检查是否启用了注解扫描
     */
    private boolean isAnnotationScanEnabled() {
        // 检查是否有@EnableDynamicThreadPool注解
        Map<String, Object> beansWithAnnotation = applicationContext.getBeansWithAnnotation(EnableDynamicThreadPool.class);
        if (!beansWithAnnotation.isEmpty()) {
            return true;
        }
        
        // 检查配置属性
        try {
            String enabled = applicationContext.getEnvironment().getProperty("dynamictp.core.annotation.enabled", "false");
            return Boolean.parseBoolean(enabled);
        } catch (Exception e) {
            log.debug("Failed to check annotation scan configuration", e);
            return false;
        }
    }

    /**
     * 处理类级别的@DynamicThreadPool注解
     */
    private void processClassLevelAnnotation(Class<?> targetClass, String beanName) {
        DynamicThreadPool annotation = AnnotationUtils.findAnnotation(targetClass, DynamicThreadPool.class);
        if (annotation != null) {
            String threadPoolName = getThreadPoolName(annotation, targetClass.getSimpleName(), beanName);
            createThreadPoolFromAnnotation(annotation, threadPoolName, targetClass.getName());
        }
    }

    /**
     * 处理方法级别的@DynamicThreadPool注解
     */
    private void processMethodLevelAnnotations(Class<?> targetClass, String beanName) {
        Method[] methods = targetClass.getDeclaredMethods();
        for (Method method : methods) {
            DynamicThreadPool annotation = AnnotationUtils.findAnnotation(method, DynamicThreadPool.class);
            if (annotation != null) {
                String threadPoolName = getThreadPoolName(annotation, method.getName(), beanName + "." + method.getName());
                createThreadPoolFromAnnotation(annotation, threadPoolName, targetClass.getName() + "." + method.getName());
            }
        }
    }

    /**
     * 获取线程池名称
     */
    private String getThreadPoolName(DynamicThreadPool annotation, String defaultName, String fallbackName) {
        if (StringUtils.hasText(annotation.threadPoolName())) {
            return annotation.threadPoolName();
        }
        return StringUtils.hasText(defaultName) ? defaultName : fallbackName;
    }

    /**
     * 从注解创建线程池
     */
    private void createThreadPoolFromAnnotation(DynamicThreadPool annotation, String threadPoolName, String source) {
        if (processedThreadPools.contains(threadPoolName)) {
            log.warn("DynamicThreadPool [{}] already exists, skipping creation from {}", threadPoolName, source);
            return;
        }

        try {
            // 创建线程池定义
            ThreadPoolDefinition definition = new ThreadPoolDefinition(annotation, threadPoolName, source);
            threadPoolDefinitions.add(definition);
            
            // 创建线程池
            DtpCoreExecutor executor = createExecutorFromDefinition(definition);
            
            // 注册线程池
            DtpCoreRegistry.registerExecutor(executor);
            processedThreadPools.add(threadPoolName);
            
            log.info("DynamicThreadPool [{}] created from annotation at {}", threadPoolName, source);
        } catch (Exception e) {
            log.error("Failed to create DynamicThreadPool [{}] from annotation at {}", threadPoolName, source, e);
        }
    }

    /**
     * 从定义创建线程池执行器
     */
    private DtpCoreExecutor createExecutorFromDefinition(ThreadPoolDefinition definition) {
        DynamicThreadPool annotation = definition.getAnnotation();
        
        // 处理最大线程数默认值
        int maximumPoolSize = annotation.maximumPoolSize();
        if (maximumPoolSize <= 0) {
            maximumPoolSize = Runtime.getRuntime().availableProcessors();
        }
        
        // 处理线程名前缀
        String threadNamePrefix = annotation.threadNamePrefix();
        if (!StringUtils.hasText(threadNamePrefix)) {
            threadNamePrefix = definition.getThreadPoolName();
        }
        
        return DtpCoreExecutorBuilder.newBuilder()
                .threadPoolName(definition.getThreadPoolName())
                .corePoolSize(annotation.corePoolSize())
                .maximumPoolSize(maximumPoolSize)
                .keepAliveTime(annotation.keepAliveTime())
                .timeUnit(annotation.timeUnit())
                .workQueue(createWorkQueue(annotation.queueType(), annotation.queueCapacity(), annotation.fair()))
                .threadFactory(new DefaultThreadFactory(threadNamePrefix))
                .rejectedExecutionHandler(createRejectedHandler(annotation.rejectedHandlerType()))
                .allowCoreThreadTimeOut(annotation.allowCoreThreadTimeOut())
                .preStartAllCoreThreads(annotation.preStartAllCoreThreads())
                .build();
    }

    /**
     * 创建工作队列
     */
    private java.util.concurrent.BlockingQueue<Runnable> createWorkQueue(QueueType queueType, int capacity, boolean fair) {
        try {
            return queueType.createQueue(capacity, fair);
        } catch (Exception e) {
            log.warn("Failed to create queue of type: {}, using default LinkedBlockingQueue. Error: {}",
                    queueType, e.getMessage());
            return QueueType.LINKED_BLOCKING_QUEUE.createQueue(capacity, fair);
        }
    }

    /**
     * 创建拒绝策略
     */
    private java.util.concurrent.RejectedExecutionHandler createRejectedHandler(RejectedExecutionHandlerType handlerType) {
        try {
            return handlerType.createHandler();
        } catch (Exception e) {
            log.warn("Failed to create rejected handler of type: {}, using default CallerRunsPolicy. Error: {}",
                    handlerType, e.getMessage());
            return RejectedExecutionHandlerType.CALLER_RUNS_POLICY.createHandler();
        }
    }

    /**
     * 获取已处理的线程池定义
     */
    public List<ThreadPoolDefinition> getThreadPoolDefinitions() {
        return new ArrayList<>(threadPoolDefinitions);
    }

    /**
     * 默认线程工厂
     */
    private static class DefaultThreadFactory implements java.util.concurrent.ThreadFactory {
        private final String namePrefix;
        private final ThreadGroup group;
        private volatile int threadNumber = 1;

        DefaultThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix != null ? namePrefix : "dtp-annotation";
            SecurityManager s = System.getSecurityManager();
            this.group = (s != null) ? s.getThreadGroup() : Thread.currentThread().getThreadGroup();
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(group, r, namePrefix + "-" + threadNumber++, 0);
            if (t.isDaemon()) {
                t.setDaemon(false);
            }
            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY);
            }
            return t;
        }
    }
}
