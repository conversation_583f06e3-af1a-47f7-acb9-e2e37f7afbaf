/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.enums;

import lombok.extern.slf4j.Slf4j;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.PriorityBlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.LinkedTransferQueue;

/**
 * 队列类型枚举
 *
 * 定义动态线程池支持的队列类型，提供类型安全的队列选择
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
public enum QueueType {

    /**
     * 基于链表的有界阻塞队列
     * 默认容量为Integer.MAX_VALUE，可指定容量
     * FIFO（先进先出）顺序
     */
    LINKED_BLOCKING_QUEUE("LinkedBlockingQueue", "基于链表的有界阻塞队列，FIFO顺序"),

    /**
     * 基于数组的有界阻塞队列
     * 必须指定容量，FIFO（先进先出）顺序
     * 支持公平性设置
     */
    ARRAY_BLOCKING_QUEUE("ArrayBlockingQueue", "基于数组的有界阻塞队列，FIFO顺序，支持公平性"),

    /**
     * 同步队列，不存储元素
     * 每个插入操作必须等待另一个线程的移除操作
     * 适用于传递性场景
     */
    SYNCHRONOUS_QUEUE("SynchronousQueue", "同步队列，不存储元素，适用于传递性场景"),

    /**
     * 基于优先级的无界阻塞队列
     * 元素按照自然顺序或Comparator排序
     * 不保证同优先级元素的顺序
     */
    PRIORITY_BLOCKING_QUEUE("PriorityBlockingQueue", "基于优先级的无界阻塞队列，元素按优先级排序"),

    /**
     * 基于链表的无界传输队列
     * 支持传输模式，生产者可以等待消费者接收
     * 性能通常优于LinkedBlockingQueue
     */
    LINKED_TRANSFER_QUEUE("LinkedTransferQueue", "基于链表的无界传输队列，支持传输模式"),

    /**
     * 延迟队列，元素只有在延迟期满后才能被取出
     * 队列头部是延迟期满后保存时间最长的元素
     * 适用于定时任务场景
     */
    DELAY_QUEUE("DelayQueue", "延迟队列，元素延迟期满后才能被取出，适用于定时任务");

    /**
     * 队列类型名称
     */
    private final String typeName;

    /**
     * 队列类型描述
     */
    private final String description;

    QueueType(String typeName, String description) {
        this.typeName = typeName;
        this.description = description;
    }

    public String getTypeName() {
        return typeName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 创建对应类型的阻塞队列实例
     *
     * @param capacity 队列容量
     * @param fair 是否公平队列（仅对ArrayBlockingQueue有效）
     * @return 阻塞队列实例
     */
    public BlockingQueue<Runnable> createQueue(int capacity, boolean fair) {
        switch (this) {
            case LINKED_BLOCKING_QUEUE:
                return capacity > 0 ? new LinkedBlockingQueue<>(capacity) : new LinkedBlockingQueue<>();
            case ARRAY_BLOCKING_QUEUE:
                if (capacity <= 0) {
                    throw new IllegalArgumentException("ArrayBlockingQueue capacity must be positive");
                }
                return new ArrayBlockingQueue<>(capacity, fair);
            case SYNCHRONOUS_QUEUE:
                return new SynchronousQueue<>(fair);
            case PRIORITY_BLOCKING_QUEUE:
                return capacity > 0 ? new PriorityBlockingQueue<>(capacity) : new PriorityBlockingQueue<>();
            case LINKED_TRANSFER_QUEUE:
                return new LinkedTransferQueue<>();
            case DELAY_QUEUE:
                // DelayQueue需要Delayed类型，但线程池需要Runnable
                // 由于类型兼容性问题，这里使用LinkedBlockingQueue作为替代
                log.warn("DelayQueue is not directly compatible with thread pools, using LinkedBlockingQueue instead");
                return new LinkedBlockingQueue<>(capacity > 0 ? capacity : Integer.MAX_VALUE);
            default:
                throw new IllegalArgumentException("Unsupported queue type: " + this);
        }
    }

    /**
     * 创建对应类型的阻塞队列实例（使用默认公平性设置）
     *
     * @param capacity 队列容量
     * @return 阻塞队列实例
     */
    public BlockingQueue<Runnable> createQueue(int capacity) {
        return createQueue(capacity, false);
    }

    /**
     * 创建对应类型的阻塞队列实例（使用默认容量和公平性设置）
     *
     * @return 阻塞队列实例
     */
    public BlockingQueue<Runnable> createQueue() {
        return createQueue(0, false);
    }

    /**
     * 根据类型名称获取队列类型枚举
     *
     * @param typeName 类型名称
     * @return 队列类型枚举
     * @throws IllegalArgumentException 如果类型名称不支持
     */
    public static QueueType fromTypeName(String typeName) {
        if (typeName == null || typeName.trim().isEmpty()) {
            return LINKED_BLOCKING_QUEUE; // 默认类型
        }

        for (QueueType type : values()) {
            if (type.typeName.equalsIgnoreCase(typeName.trim())) {
                return type;
            }
        }

        throw new IllegalArgumentException("Unsupported queue type: " + typeName);
    }

    /**
     * 检查队列类型是否需要指定容量
     *
     * @return 如果需要指定容量返回true
     */
    public boolean requiresCapacity() {
        return this == ARRAY_BLOCKING_QUEUE;
    }

    /**
     * 检查队列类型是否支持公平性设置
     *
     * @return 如果支持公平性设置返回true
     */
    public boolean supportsFairness() {
        return this == ARRAY_BLOCKING_QUEUE || this == SYNCHRONOUS_QUEUE;
    }

    /**
     * 检查队列类型是否为有界队列
     *
     * @return 如果为有界队列返回true
     */
    public boolean isBounded() {
        return this == ARRAY_BLOCKING_QUEUE || this == SYNCHRONOUS_QUEUE;
    }

    @Override
    public String toString() {
        return typeName + " (" + description + ")";
    }


}
