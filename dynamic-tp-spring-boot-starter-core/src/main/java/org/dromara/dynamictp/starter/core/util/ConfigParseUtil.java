/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.util;

import lombok.extern.slf4j.Slf4j;
import org.dromara.dynamictp.starter.core.properties.DtpCoreProperties;
import org.yaml.snakeyaml.Yaml;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * 配置解析工具类
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
public class ConfigParseUtil {

    private static final String DYNAMICTP_PREFIX = "dynamictp";
    private static final String CORE_PREFIX = "core";
    private static final String EXECUTORS_KEY = "executors";

    /**
     * 解析YAML配置
     */
    public static DtpCoreProperties parseYamlConfig(String yamlContent) {
        try {
            Yaml yaml = new Yaml();
            Map<String, Object> yamlMap = yaml.load(yamlContent);
            return parseFromMap(yamlMap);
        } catch (Exception e) {
            log.error("ConfigParseUtil parse yaml config failed", e);
            return null;
        }
    }

    /**
     * 解析Properties配置
     */
    public static DtpCoreProperties parsePropertiesConfig(String propertiesContent) {
        try {
            Properties props = new Properties();
            props.load(new java.io.StringReader(propertiesContent));
            return parseFromProperties(props);
        } catch (Exception e) {
            log.error("ConfigParseUtil parse properties config failed", e);
            return null;
        }
    }

    /**
     * 从Map解析配置
     */
    @SuppressWarnings("unchecked")
    private static DtpCoreProperties parseFromMap(Map<String, Object> configMap) {
        if (configMap == null) {
            return null;
        }

        DtpCoreProperties properties = new DtpCoreProperties();

        // 解析 dynamictp.core 配置
        Map<String, Object> dynamicTpMap = (Map<String, Object>) configMap.get(DYNAMICTP_PREFIX);
        if (dynamicTpMap == null) {
            return properties;
        }

        Map<String, Object> coreMap = (Map<String, Object>) dynamicTpMap.get(CORE_PREFIX);
        if (coreMap == null) {
            return properties;
        }

        // 解析基础配置
        if (coreMap.containsKey("enabled")) {
            properties.setEnabled(Boolean.parseBoolean(coreMap.get("enabled").toString()));
        }
        if (coreMap.containsKey("enabledBanner")) {
            properties.setEnabledBanner(Boolean.parseBoolean(coreMap.get("enabledBanner").toString()));
        }
        if (coreMap.containsKey("env")) {
            properties.setEnv(coreMap.get("env").toString());
        }

        // 解析线程池配置
        List<Object> executorsList = (List<Object>) coreMap.get(EXECUTORS_KEY);
        if (executorsList != null) {
            List<DtpCoreProperties.ThreadPoolProperties> executors = new ArrayList<>();
            for (Object executorObj : executorsList) {
                if (executorObj instanceof Map) {
                    Map<String, Object> executorMap = (Map<String, Object>) executorObj;
                    DtpCoreProperties.ThreadPoolProperties executor = parseThreadPoolProperties(executorMap);
                    if (executor != null) {
                        executors.add(executor);
                    }
                }
            }
            properties.setExecutors(executors);
        }

        // 解析Nacos配置
        Map<String, Object> nacosMap = (Map<String, Object>) coreMap.get("nacos");
        if (nacosMap != null) {
            DtpCoreProperties.NacosConfig nacosConfig = parseNacosConfig(nacosMap);
            properties.setNacos(nacosConfig);
        }

        return properties;
    }

    /**
     * 解析线程池属性
     */
    private static DtpCoreProperties.ThreadPoolProperties parseThreadPoolProperties(Map<String, Object> executorMap) {
        DtpCoreProperties.ThreadPoolProperties props = new DtpCoreProperties.ThreadPoolProperties();

        if (executorMap.containsKey("threadPoolName")) {
            props.setThreadPoolName(executorMap.get("threadPoolName").toString());
        }
        if (executorMap.containsKey("corePoolSize")) {
            props.setCorePoolSize(Integer.parseInt(executorMap.get("corePoolSize").toString()));
        }
        if (executorMap.containsKey("maximumPoolSize")) {
            props.setMaximumPoolSize(Integer.parseInt(executorMap.get("maximumPoolSize").toString()));
        }
        if (executorMap.containsKey("keepAliveTime")) {
            props.setKeepAliveTime(Long.parseLong(executorMap.get("keepAliveTime").toString()));
        }
        if (executorMap.containsKey("unit")) {
            props.setUnit(executorMap.get("unit").toString());
        }
        if (executorMap.containsKey("queueType")) {
            props.setQueueType(executorMap.get("queueType").toString());
        }
        if (executorMap.containsKey("queueCapacity")) {
            props.setQueueCapacity(Integer.parseInt(executorMap.get("queueCapacity").toString()));
        }
        if (executorMap.containsKey("rejectedHandlerType")) {
            props.setRejectedHandlerType(executorMap.get("rejectedHandlerType").toString());
        }
        if (executorMap.containsKey("threadNamePrefix")) {
            props.setThreadNamePrefix(executorMap.get("threadNamePrefix").toString());
        }
        if (executorMap.containsKey("allowCoreThreadTimeOut")) {
            props.setAllowCoreThreadTimeOut(Boolean.parseBoolean(executorMap.get("allowCoreThreadTimeOut").toString()));
        }
        if (executorMap.containsKey("preStartAllCoreThreads")) {
            props.setPreStartAllCoreThreads(Boolean.parseBoolean(executorMap.get("preStartAllCoreThreads").toString()));
        }
        if (executorMap.containsKey("fair")) {
            props.setFair(Boolean.parseBoolean(executorMap.get("fair").toString()));
        }

        return props;
    }

    /**
     * 解析Nacos配置
     */
    private static DtpCoreProperties.NacosConfig parseNacosConfig(Map<String, Object> nacosMap) {
        DtpCoreProperties.NacosConfig nacosConfig = new DtpCoreProperties.NacosConfig();

        if (nacosMap.containsKey("serverAddr")) {
            nacosConfig.setServerAddr(nacosMap.get("serverAddr").toString());
        }
        if (nacosMap.containsKey("namespace")) {
            nacosConfig.setNamespace(nacosMap.get("namespace").toString());
        }
        if (nacosMap.containsKey("group")) {
            nacosConfig.setGroup(nacosMap.get("group").toString());
        }
        if (nacosMap.containsKey("dataId")) {
            nacosConfig.setDataId(nacosMap.get("dataId").toString());
        }
        if (nacosMap.containsKey("type")) {
            nacosConfig.setType(nacosMap.get("type").toString());
        }
        if (nacosMap.containsKey("username")) {
            nacosConfig.setUsername(nacosMap.get("username").toString());
        }
        if (nacosMap.containsKey("password")) {
            nacosConfig.setPassword(nacosMap.get("password").toString());
        }
        if (nacosMap.containsKey("accessKey")) {
            nacosConfig.setAccessKey(nacosMap.get("accessKey").toString());
        }
        if (nacosMap.containsKey("secretKey")) {
            nacosConfig.setSecretKey(nacosMap.get("secretKey").toString());
        }
        if (nacosMap.containsKey("connectTimeout")) {
            nacosConfig.setConnectTimeout(Long.parseLong(nacosMap.get("connectTimeout").toString()));
        }
        if (nacosMap.containsKey("readTimeout")) {
            nacosConfig.setReadTimeout(Long.parseLong(nacosMap.get("readTimeout").toString()));
        }

        return nacosConfig;
    }

    /**
     * 从Properties解析配置
     */
    private static DtpCoreProperties parseFromProperties(Properties props) {
        DtpCoreProperties properties = new DtpCoreProperties();

        // 解析基础配置
        String enabled = props.getProperty("dynamictp.core.enabled");
        if (enabled != null) {
            properties.setEnabled(Boolean.parseBoolean(enabled));
        }

        String enabledBanner = props.getProperty("dynamictp.core.enabledBanner");
        if (enabledBanner != null) {
            properties.setEnabledBanner(Boolean.parseBoolean(enabledBanner));
        }

        String env = props.getProperty("dynamictp.core.env");
        if (env != null) {
            properties.setEnv(env);
        }

        // 解析线程池配置（简化实现）
        // 实际项目中可以实现更完整的Properties解析逻辑

        return properties;
    }
}
