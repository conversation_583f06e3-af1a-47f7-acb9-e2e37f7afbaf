/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.annotation;

import java.lang.annotation.*;

/**
 * 动态线程池注解
 * 
 * 用于标记需要动态管理的线程池，支持配置线程池的核心参数。
 * 可以应用在方法上或类上，自动创建对应的DtpCoreExecutor实例。
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DynamicThreadPool {

    /**
     * 线程池名称，必须唯一
     * 如果不指定，将使用方法名或类名作为默认值
     */
    String threadPoolName() default "";

    /**
     * 核心线程数
     * 默认值：1
     */
    int corePoolSize() default 1;

    /**
     * 最大线程数
     * 默认值：CPU核心数
     */
    int maximumPoolSize() default -1;

    /**
     * 线程存活时间
     * 默认值：60秒
     */
    long keepAliveTime() default 60L;

    /**
     * 时间单位
     * 支持：NANOSECONDS, MICROSECONDS, MILLISECONDS, SECONDS, MINUTES, HOURS, DAYS
     * 默认值：SECONDS
     */
    String timeUnit() default "SECONDS";

    /**
     * 队列类型
     * 支持：LinkedBlockingQueue, ArrayBlockingQueue, SynchronousQueue, PriorityBlockingQueue, VariableLinkedBlockingQueue
     * 默认值：LinkedBlockingQueue
     */
    String queueType() default "LinkedBlockingQueue";

    /**
     * 队列容量
     * 默认值：1024
     */
    int queueCapacity() default 1024;

    /**
     * 拒绝策略
     * 支持：AbortPolicy, CallerRunsPolicy, DiscardPolicy, DiscardOldestPolicy
     * 默认值：CallerRunsPolicy
     */
    String rejectedHandlerType() default "CallerRunsPolicy";

    /**
     * 线程名前缀
     * 如果不指定，将使用线程池名称作为前缀
     */
    String threadNamePrefix() default "";

    /**
     * 是否允许核心线程超时
     * 默认值：false
     */
    boolean allowCoreThreadTimeOut() default false;

    /**
     * 是否预启动所有核心线程
     * 默认值：false
     */
    boolean preStartAllCoreThreads() default false;

    /**
     * 是否公平队列（仅对支持公平性的队列有效）
     * 默认值：false
     */
    boolean fair() default false;

    /**
     * 是否启用动态配置刷新
     * 默认值：true
     */
    boolean enableRefresh() default true;

    /**
     * 线程池描述信息
     */
    String description() default "";

    /**
     * 优先级，用于控制线程池创建顺序
     * 数值越小优先级越高
     * 默认值：0
     */
    int priority() default 0;
}
