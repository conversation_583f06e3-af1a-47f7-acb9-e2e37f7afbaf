/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.annotation;

import org.dromara.dynamictp.starter.core.processor.DynamicThreadPoolAnnotationProcessor;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * 启用动态线程池注解扫描
 * 
 * 在Spring Boot应用的主类或配置类上添加此注解，
 * 可以启用@DynamicThreadPool注解的自动扫描和处理功能。
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import(DynamicThreadPoolAnnotationProcessor.class)
public @interface EnableDynamicThreadPool {

    /**
     * 指定扫描的基础包路径
     * 如果不指定，将扫描启用注解的类所在包及其子包
     */
    String[] basePackages() default {};

    /**
     * 指定扫描的基础类
     * 将扫描这些类所在包及其子包
     */
    Class<?>[] basePackageClasses() default {};

    /**
     * 是否启用注解扫描
     * 默认值：true
     */
    boolean enabled() default true;

    /**
     * 扫描优先级
     * 数值越小优先级越高，越早执行扫描
     * 默认值：0
     */
    int order() default 0;
}
