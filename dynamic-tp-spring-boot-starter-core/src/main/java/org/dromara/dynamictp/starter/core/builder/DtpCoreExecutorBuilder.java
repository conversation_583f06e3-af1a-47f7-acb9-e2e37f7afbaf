/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.builder;

import lombok.extern.slf4j.Slf4j;
import org.dromara.dynamictp.starter.core.executor.DtpCoreExecutor;
import org.dromara.dynamictp.starter.core.properties.DtpCoreProperties;
import org.dromara.dynamictp.starter.core.registry.DtpCoreRegistry;

import java.util.concurrent.*;

/**
 * 轻量级动态线程池构建器
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
public class DtpCoreExecutorBuilder {

    private String threadPoolName;
    private int corePoolSize = 1;
    private int maximumPoolSize = Runtime.getRuntime().availableProcessors();
    private long keepAliveTime = 60L;
    private TimeUnit unit = TimeUnit.SECONDS;
    private BlockingQueue<Runnable> workQueue;
    private ThreadFactory threadFactory;
    private RejectedExecutionHandler handler;
    private boolean allowCoreThreadTimeOut = false;
    private boolean preStartAllCoreThreads = false;

    public static DtpCoreExecutorBuilder newBuilder() {
        return new DtpCoreExecutorBuilder();
    }

    public DtpCoreExecutorBuilder threadPoolName(String threadPoolName) {
        this.threadPoolName = threadPoolName;
        return this;
    }

    public DtpCoreExecutorBuilder corePoolSize(int corePoolSize) {
        this.corePoolSize = corePoolSize;
        return this;
    }

    public DtpCoreExecutorBuilder maximumPoolSize(int maximumPoolSize) {
        this.maximumPoolSize = maximumPoolSize;
        return this;
    }

    public DtpCoreExecutorBuilder keepAliveTime(long keepAliveTime) {
        this.keepAliveTime = keepAliveTime;
        return this;
    }

    public DtpCoreExecutorBuilder timeUnit(TimeUnit unit) {
        this.unit = unit;
        return this;
    }

    public DtpCoreExecutorBuilder workQueue(BlockingQueue<Runnable> workQueue) {
        this.workQueue = workQueue;
        return this;
    }

    public DtpCoreExecutorBuilder threadFactory(ThreadFactory threadFactory) {
        this.threadFactory = threadFactory;
        return this;
    }

    public DtpCoreExecutorBuilder rejectedExecutionHandler(RejectedExecutionHandler handler) {
        this.handler = handler;
        return this;
    }

    public DtpCoreExecutorBuilder allowCoreThreadTimeOut(boolean allowCoreThreadTimeOut) {
        this.allowCoreThreadTimeOut = allowCoreThreadTimeOut;
        return this;
    }

    public DtpCoreExecutorBuilder preStartAllCoreThreads(boolean preStartAllCoreThreads) {
        this.preStartAllCoreThreads = preStartAllCoreThreads;
        return this;
    }

    /**
     * 构建线程池
     */
    public DtpCoreExecutor build() {
        // 设置默认值
        if (workQueue == null) {
            workQueue = new LinkedBlockingQueue<>(1024);
        }
        if (threadFactory == null) {
            threadFactory = new DefaultThreadFactory(threadPoolName);
        }
        if (handler == null) {
            handler = new ThreadPoolExecutor.CallerRunsPolicy();
        }

        DtpCoreExecutor executor = new DtpCoreExecutor(
                corePoolSize, maximumPoolSize, keepAliveTime, unit,
                workQueue, threadFactory, handler);

        executor.setThreadPoolName(threadPoolName);
        executor.allowCoreThreadTimeOut(allowCoreThreadTimeOut);
        
        if (preStartAllCoreThreads) {
            executor.prestartAllCoreThreads();
        }

        return executor;
    }

    /**
     * 构建并注册线程池
     */
    public DtpCoreExecutor buildAndRegister() {
        DtpCoreExecutor executor = build();
        DtpCoreRegistry.registerExecutor(executor);
        return executor;
    }

    /**
     * 从配置属性构建线程池
     */
    public static DtpCoreExecutor buildFromProperties(DtpCoreProperties.ThreadPoolProperties props) {
        if (props.getThreadPoolName() == null) {
            throw new IllegalArgumentException("ThreadPool name cannot be null");
        }

        return newBuilder()
                .threadPoolName(props.getThreadPoolName())
                .corePoolSize(props.getCorePoolSize())
                .maximumPoolSize(props.getMaximumPoolSize())
                .keepAliveTime(props.getKeepAliveTime())
                .timeUnit(TimeUnit.valueOf(props.getUnit().toUpperCase()))
                .workQueue(createWorkQueue(props.getQueueType(), props.getQueueCapacity(), props.isFair()))
                .threadFactory(new DefaultThreadFactory(props.getThreadNamePrefix()))
                .rejectedExecutionHandler(createRejectedHandler(props.getRejectedHandlerType()))
                .allowCoreThreadTimeOut(props.isAllowCoreThreadTimeOut())
                .preStartAllCoreThreads(props.isPreStartAllCoreThreads())
                .build();
    }

    /**
     * 创建工作队列
     */
    private static BlockingQueue<Runnable> createWorkQueue(String queueType, int capacity, boolean fair) {
        switch (queueType) {
            case "ArrayBlockingQueue":
                return new ArrayBlockingQueue<>(capacity, fair);
            case "LinkedBlockingQueue":
                return new LinkedBlockingQueue<>(capacity);
            case "SynchronousQueue":
                return new SynchronousQueue<>(fair);
            case "PriorityBlockingQueue":
                return new PriorityBlockingQueue<>(capacity);
            case "VariableLinkedBlockingQueue":
            default:
                return new LinkedBlockingQueue<>(capacity);
        }
    }

    /**
     * 创建拒绝策略
     */
    private static RejectedExecutionHandler createRejectedHandler(String handlerType) {
        switch (handlerType) {
            case "AbortPolicy":
                return new ThreadPoolExecutor.AbortPolicy();
            case "CallerRunsPolicy":
                return new ThreadPoolExecutor.CallerRunsPolicy();
            case "DiscardPolicy":
                return new ThreadPoolExecutor.DiscardPolicy();
            case "DiscardOldestPolicy":
                return new ThreadPoolExecutor.DiscardOldestPolicy();
            default:
                return new ThreadPoolExecutor.CallerRunsPolicy();
        }
    }

    /**
     * 默认线程工厂
     */
    private static class DefaultThreadFactory implements ThreadFactory {
        private final String namePrefix;
        private final ThreadGroup group;
        private volatile int threadNumber = 1;

        DefaultThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix != null ? namePrefix : "dtp-core";
            SecurityManager s = System.getSecurityManager();
            this.group = (s != null) ? s.getThreadGroup() : Thread.currentThread().getThreadGroup();
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(group, r, namePrefix + "-" + threadNumber++, 0);
            if (t.isDaemon()) {
                t.setDaemon(false);
            }
            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY);
            }
            return t;
        }
    }
}
