/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.executor;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 轻量级动态线程池执行器
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
@Getter
@Setter
public class DtpCoreExecutor extends ThreadPoolExecutor {

    /**
     * 线程池名称
     */
    private String threadPoolName;

    /**
     * 任务执行计数器
     */
    private final AtomicLong executeCount = new AtomicLong(0);

    /**
     * 任务拒绝计数器
     */
    private final AtomicLong rejectCount = new AtomicLong(0);

    /**
     * 创建时间
     */
    private final long createTime = System.currentTimeMillis();

    public DtpCoreExecutor(int corePoolSize,
                           int maximumPoolSize,
                           long keepAliveTime,
                           TimeUnit unit,
                           BlockingQueue<Runnable> workQueue,
                           ThreadFactory threadFactory,
                           RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
    }

    @Override
    public void execute(Runnable command) {
        executeCount.incrementAndGet();
        try {
            super.execute(command);
        } catch (RejectedExecutionException e) {
            rejectCount.incrementAndGet();
            log.warn("DtpCoreExecutor [{}] rejected task execution", threadPoolName, e);
            throw e;
        }
    }

    @Override
    protected void beforeExecute(Thread t, Runnable r) {
        super.beforeExecute(t, r);
        if (log.isDebugEnabled()) {
            log.debug("DtpCoreExecutor [{}] before execute task: {}", threadPoolName, r.getClass().getSimpleName());
        }
    }

    @Override
    protected void afterExecute(Runnable r, Throwable t) {
        super.afterExecute(r, t);
        if (t != null) {
            log.error("DtpCoreExecutor [{}] task execution failed", threadPoolName, t);
        }
        if (log.isDebugEnabled()) {
            log.debug("DtpCoreExecutor [{}] after execute task: {}", threadPoolName, r.getClass().getSimpleName());
        }
    }

    /**
     * 刷新线程池配置
     */
    public void refresh(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit) {
        log.info("DtpCoreExecutor [{}] refresh config: corePoolSize={}, maximumPoolSize={}, keepAliveTime={}{}",
                threadPoolName, corePoolSize, maximumPoolSize, keepAliveTime, unit);
        
        setCorePoolSize(corePoolSize);
        setMaximumPoolSize(maximumPoolSize);
        setKeepAliveTime(keepAliveTime, unit);
    }

    /**
     * 获取线程池统计信息
     */
    public ThreadPoolStats getStats() {
        return ThreadPoolStats.builder()
                .threadPoolName(threadPoolName)
                .corePoolSize(getCorePoolSize())
                .maximumPoolSize(getMaximumPoolSize())
                .activeCount(getActiveCount())
                .poolSize(getPoolSize())
                .largestPoolSize(getLargestPoolSize())
                .taskCount(getTaskCount())
                .completedTaskCount(getCompletedTaskCount())
                .queueSize(getQueue().size())
                .queueCapacity(getQueueCapacity())
                .executeCount(executeCount.get())
                .rejectCount(rejectCount.get())
                .createTime(createTime)
                .build();
    }

    private int getQueueCapacity() {
        BlockingQueue<Runnable> queue = getQueue();
        if (queue instanceof LinkedBlockingQueue) {
            return queue.remainingCapacity() + queue.size();
        }
        return queue.remainingCapacity();
    }

    /**
     * 线程池统计信息
     */
    @lombok.Builder
    @lombok.Data
    public static class ThreadPoolStats {
        private String threadPoolName;
        private int corePoolSize;
        private int maximumPoolSize;
        private int activeCount;
        private int poolSize;
        private int largestPoolSize;
        private long taskCount;
        private long completedTaskCount;
        private int queueSize;
        private int queueCapacity;
        private long executeCount;
        private long rejectCount;
        private long createTime;
    }
}
