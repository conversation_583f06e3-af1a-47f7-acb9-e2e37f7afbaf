/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.enums;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 拒绝执行处理器类型枚举
 * 
 * 定义动态线程池支持的拒绝策略类型，提供类型安全的策略选择
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public enum RejectedExecutionHandlerType {

    /**
     * 中止策略（默认策略）
     * 抛出RejectedExecutionException异常
     * 适用于需要明确知道任务被拒绝的场景
     */
    ABORT_POLICY("AbortPolicy", "中止策略，抛出RejectedExecutionException异常"),

    /**
     * 调用者运行策略
     * 由调用线程直接执行被拒绝的任务
     * 适用于不希望丢失任务的场景，但会降低提交任务的速度
     */
    CALLER_RUNS_POLICY("CallerRunsPolicy", "调用者运行策略，由调用线程直接执行任务"),

    /**
     * 丢弃策略
     * 静默丢弃被拒绝的任务，不抛出异常
     * 适用于可以容忍任务丢失的场景
     */
    DISCARD_POLICY("DiscardPolicy", "丢弃策略，静默丢弃被拒绝的任务"),

    /**
     * 丢弃最旧策略
     * 丢弃队列中最旧的任务，然后尝试重新提交当前任务
     * 适用于新任务比旧任务更重要的场景
     */
    DISCARD_OLDEST_POLICY("DiscardOldestPolicy", "丢弃最旧策略，丢弃队列中最旧的任务");

    /**
     * 策略类型名称
     */
    private final String typeName;

    /**
     * 策略类型描述
     */
    private final String description;

    RejectedExecutionHandlerType(String typeName, String description) {
        this.typeName = typeName;
        this.description = description;
    }

    public String getTypeName() {
        return typeName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 创建对应类型的拒绝执行处理器实例
     *
     * @return 拒绝执行处理器实例
     */
    public RejectedExecutionHandler createHandler() {
        switch (this) {
            case ABORT_POLICY:
                return new ThreadPoolExecutor.AbortPolicy();
            case CALLER_RUNS_POLICY:
                return new ThreadPoolExecutor.CallerRunsPolicy();
            case DISCARD_POLICY:
                return new ThreadPoolExecutor.DiscardPolicy();
            case DISCARD_OLDEST_POLICY:
                return new ThreadPoolExecutor.DiscardOldestPolicy();
            default:
                throw new IllegalArgumentException("Unsupported rejected execution handler type: " + this);
        }
    }

    /**
     * 根据类型名称获取拒绝策略类型枚举
     *
     * @param typeName 类型名称
     * @return 拒绝策略类型枚举
     * @throws IllegalArgumentException 如果类型名称不支持
     */
    public static RejectedExecutionHandlerType fromTypeName(String typeName) {
        if (typeName == null || typeName.trim().isEmpty()) {
            return CALLER_RUNS_POLICY; // 默认策略
        }

        for (RejectedExecutionHandlerType type : values()) {
            if (type.typeName.equalsIgnoreCase(typeName.trim())) {
                return type;
            }
        }

        throw new IllegalArgumentException("Unsupported rejected execution handler type: " + typeName);
    }

    /**
     * 检查策略是否会抛出异常
     *
     * @return 如果会抛出异常返回true
     */
    public boolean throwsException() {
        return this == ABORT_POLICY;
    }

    /**
     * 检查策略是否会丢失任务
     *
     * @return 如果会丢失任务返回true
     */
    public boolean losesTask() {
        return this == DISCARD_POLICY || this == DISCARD_OLDEST_POLICY;
    }

    /**
     * 检查策略是否会阻塞调用线程
     *
     * @return 如果会阻塞调用线程返回true
     */
    public boolean blocksCallerThread() {
        return this == CALLER_RUNS_POLICY;
    }

    /**
     * 获取策略的性能影响级别
     *
     * @return 性能影响级别（1-低，2-中，3-高）
     */
    public int getPerformanceImpact() {
        switch (this) {
            case DISCARD_POLICY:
                return 1; // 最低影响，直接丢弃
            case DISCARD_OLDEST_POLICY:
                return 2; // 中等影响，需要操作队列
            case ABORT_POLICY:
                return 2; // 中等影响，需要创建异常
            case CALLER_RUNS_POLICY:
                return 3; // 最高影响，阻塞调用线程
            default:
                return 2;
        }
    }

    @Override
    public String toString() {
        return typeName + " (" + description + ")";
    }
}
