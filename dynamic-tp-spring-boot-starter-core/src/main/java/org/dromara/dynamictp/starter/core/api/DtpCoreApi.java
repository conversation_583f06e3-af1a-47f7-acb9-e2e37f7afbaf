/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.api;

import org.dromara.dynamictp.starter.core.executor.DtpCoreExecutor;
import org.dromara.dynamictp.starter.core.registry.DtpCoreRegistry;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 动态线程池核心API
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public class DtpCoreApi {

    /**
     * 获取线程池执行器
     */
    public static DtpCoreExecutor getExecutor(String poolName) {
        return DtpCoreRegistry.getExecutor(poolName);
    }

    /**
     * 获取所有线程池执行器
     */
    public static Map<String, DtpCoreExecutor> getAllExecutors() {
        return DtpCoreRegistry.getAllExecutors();
    }

    /**
     * 获取线程池统计信息
     */
    public static DtpCoreExecutor.ThreadPoolStats getThreadPoolStats(String poolName) {
        DtpCoreExecutor executor = DtpCoreRegistry.getExecutor(poolName);
        return executor != null ? executor.getStats() : null;
    }

    /**
     * 获取所有线程池统计信息
     */
    public static List<DtpCoreExecutor.ThreadPoolStats> getAllThreadPoolStats() {
        return DtpCoreRegistry.getAllExecutors().values().stream()
                .map(DtpCoreExecutor::getStats)
                .collect(Collectors.toList());
    }

    /**
     * 检查线程池是否存在
     */
    public static boolean containsExecutor(String poolName) {
        return DtpCoreRegistry.containsExecutor(poolName);
    }

    /**
     * 获取注册的线程池数量
     */
    public static int getExecutorCount() {
        return DtpCoreRegistry.getExecutorCount();
    }

    /**
     * 获取线程池名称列表
     */
    public static List<String> getExecutorNames() {
        return DtpCoreRegistry.getAllExecutors().keySet().stream()
                .collect(Collectors.toList());
    }
}
