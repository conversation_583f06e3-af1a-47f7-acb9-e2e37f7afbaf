/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.registry;

import lombok.extern.slf4j.Slf4j;
import org.dromara.dynamictp.starter.core.executor.DtpCoreExecutor;
import org.dromara.dynamictp.starter.core.properties.DtpCoreProperties;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 轻量级动态线程池注册中心
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
public class DtpCoreRegistry {

    /**
     * 线程池注册表
     */
    private static final Map<String, DtpCoreExecutor> EXECUTOR_REGISTRY = new ConcurrentHashMap<>();

    /**
     * 注册线程池
     */
    public static void registerExecutor(DtpCoreExecutor executor) {
        if (executor == null || executor.getThreadPoolName() == null) {
            log.warn("DtpCoreRegistry register failed, executor or threadPoolName is null");
            return;
        }
        
        String poolName = executor.getThreadPoolName();
        if (EXECUTOR_REGISTRY.containsKey(poolName)) {
            log.warn("DtpCoreRegistry register failed, threadPool [{}] already exists", poolName);
            return;
        }
        
        EXECUTOR_REGISTRY.put(poolName, executor);
        log.info("DtpCoreRegistry register success, threadPool: {}", poolName);
    }

    /**
     * 获取线程池
     */
    public static DtpCoreExecutor getExecutor(String poolName) {
        return EXECUTOR_REGISTRY.get(poolName);
    }

    /**
     * 获取所有线程池
     */
    public static Map<String, DtpCoreExecutor> getAllExecutors() {
        return new ConcurrentHashMap<>(EXECUTOR_REGISTRY);
    }

    /**
     * 移除线程池
     */
    public static DtpCoreExecutor removeExecutor(String poolName) {
        DtpCoreExecutor executor = EXECUTOR_REGISTRY.remove(poolName);
        if (executor != null) {
            log.info("DtpCoreRegistry remove success, threadPool: {}", poolName);
        }
        return executor;
    }

    /**
     * 刷新线程池配置
     */
    public static void refresh(DtpCoreProperties properties) {
        if (properties == null || properties.getExecutors() == null) {
            log.debug("DtpCoreRegistry refresh, empty thread pool properties");
            return;
        }

        for (DtpCoreProperties.ThreadPoolProperties props : properties.getExecutors()) {
            refresh(props);
        }
    }

    /**
     * 刷新单个线程池配置
     */
    public static void refresh(DtpCoreProperties.ThreadPoolProperties props) {
        if (props == null || props.getThreadPoolName() == null) {
            log.warn("DtpCoreRegistry refresh failed, props or threadPoolName is null");
            return;
        }

        String poolName = props.getThreadPoolName();
        DtpCoreExecutor executor = EXECUTOR_REGISTRY.get(poolName);
        if (executor == null) {
            log.warn("DtpCoreRegistry refresh failed, threadPool [{}] not found", poolName);
            return;
        }

        try {
            TimeUnit unit = TimeUnit.valueOf(props.getUnit().toUpperCase());
            executor.refresh(props.getCorePoolSize(), props.getMaximumPoolSize(), 
                           props.getKeepAliveTime(), unit);
            log.info("DtpCoreRegistry refresh success, threadPool: {}", poolName);
        } catch (Exception e) {
            log.error("DtpCoreRegistry refresh failed, threadPool: {}", poolName, e);
        }
    }

    /**
     * 关闭所有线程池
     */
    public static void shutdown() {
        log.info("DtpCoreRegistry shutdown start, total executors: {}", EXECUTOR_REGISTRY.size());
        
        for (Map.Entry<String, DtpCoreExecutor> entry : EXECUTOR_REGISTRY.entrySet()) {
            String poolName = entry.getKey();
            DtpCoreExecutor executor = entry.getValue();
            
            try {
                executor.shutdown();
                if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                    if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                        log.warn("DtpCoreRegistry shutdown timeout, threadPool: {}", poolName);
                    }
                }
                log.info("DtpCoreRegistry shutdown success, threadPool: {}", poolName);
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
                log.warn("DtpCoreRegistry shutdown interrupted, threadPool: {}", poolName);
            } catch (Exception e) {
                log.error("DtpCoreRegistry shutdown error, threadPool: {}", poolName, e);
            }
        }
        
        EXECUTOR_REGISTRY.clear();
        log.info("DtpCoreRegistry shutdown completed");
    }

    /**
     * 获取注册的线程池数量
     */
    public static int getExecutorCount() {
        return EXECUTOR_REGISTRY.size();
    }

    /**
     * 检查线程池是否存在
     */
    public static boolean containsExecutor(String poolName) {
        return EXECUTOR_REGISTRY.containsKey(poolName);
    }
}
