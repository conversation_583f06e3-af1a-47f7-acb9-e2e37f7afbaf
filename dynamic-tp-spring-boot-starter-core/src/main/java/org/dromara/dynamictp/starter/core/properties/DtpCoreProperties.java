/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * 轻量级动态线程池核心配置属性
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
@ConfigurationProperties(prefix = "dynamictp.core")
public class DtpCoreProperties {

    /**
     * 是否启用动态线程池
     */
    private boolean enabled = true;

    /**
     * 是否打印banner
     */
    private boolean enabledBanner = true;

    /**
     * 应用环境
     */
    private String env;

    /**
     * 线程池配置列表
     */
    private List<ThreadPoolProperties> executors = new ArrayList<>();

    /**
     * Nacos配置中心配置
     */
    private NacosConfig nacos = new NacosConfig();

    /**
     * 注解配置
     */
    private AnnotationConfig annotation = new AnnotationConfig();

    /**
     * 线程池配置属性
     */
    @Data
    public static class ThreadPoolProperties {
        
        /**
         * 线程池名称
         */
        private String threadPoolName;

        /**
         * 核心线程数
         */
        private int corePoolSize = 1;

        /**
         * 最大线程数
         */
        private int maximumPoolSize = Runtime.getRuntime().availableProcessors();

        /**
         * 线程存活时间
         */
        private long keepAliveTime = 60L;

        /**
         * 时间单位
         */
        private String unit = "SECONDS";

        /**
         * 队列类型
         */
        private String queueType = "VariableLinkedBlockingQueue";

        /**
         * 队列容量
         */
        private int queueCapacity = 1024;

        /**
         * 拒绝策略
         */
        private String rejectedHandlerType = "CallerRunsPolicy";

        /**
         * 线程名前缀
         */
        private String threadNamePrefix;

        /**
         * 是否允许核心线程超时
         */
        private boolean allowCoreThreadTimeOut = false;

        /**
         * 是否预启动所有核心线程
         */
        private boolean preStartAllCoreThreads = false;

        /**
         * 是否公平队列
         */
        private boolean fair = false;
    }

    /**
     * Nacos配置中心配置
     */
    @Data
    public static class NacosConfig {
        
        /**
         * Nacos服务器地址
         */
        private String serverAddr = "127.0.0.1:8848";

        /**
         * 命名空间
         */
        private String namespace = "";

        /**
         * 配置组
         */
        private String group = "DEFAULT_GROUP";

        /**
         * 配置数据ID
         */
        private String dataId = "dynamic-tp-config.yml";

        /**
         * 配置类型
         */
        private String type = "yaml";

        /**
         * 用户名
         */
        private String username;

        /**
         * 密码
         */
        private String password;

        /**
         * 访问密钥
         */
        private String accessKey;

        /**
         * 密钥
         */
        private String secretKey;

        /**
         * 连接超时时间
         */
        private long connectTimeout = 3000L;

        /**
         * 读取超时时间
         */
        private long readTimeout = 5000L;
    }

    /**
     * 注解配置
     */
    @Data
    public static class AnnotationConfig {

        /**
         * 是否启用注解扫描
         */
        private boolean enabled = true;

        /**
         * 扫描的基础包路径
         */
        private String[] basePackages = {};

        /**
         * 扫描优先级
         */
        private int order = 0;

        /**
         * 是否启用方法级别注解
         */
        private boolean enableMethodLevel = true;

        /**
         * 是否启用类级别注解
         */
        private boolean enableClassLevel = true;
    }
}
