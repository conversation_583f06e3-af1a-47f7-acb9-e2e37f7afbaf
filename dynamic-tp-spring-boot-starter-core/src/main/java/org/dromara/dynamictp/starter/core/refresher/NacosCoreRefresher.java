/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.refresher;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.listener.Listener;
import com.alibaba.nacos.api.exception.NacosException;
import lombok.extern.slf4j.Slf4j;
import org.dromara.dynamictp.starter.core.properties.DtpCoreProperties;
import org.dromara.dynamictp.starter.core.registry.DtpCoreRegistry;
import org.dromara.dynamictp.starter.core.util.ConfigParseUtil;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Properties;
import java.util.concurrent.Executor;

/**
 * Nacos配置刷新器
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Slf4j
public class NacosCoreRefresher {

    private final DtpCoreProperties dtpCoreProperties;
    private ConfigService configService;
    private boolean initialized = false;

    public NacosCoreRefresher(DtpCoreProperties dtpCoreProperties) {
        this.dtpCoreProperties = dtpCoreProperties;
    }

    /**
     * 初始化Nacos配置监听
     */
    public void initialize() {
        if (initialized) {
            return;
        }

        try {
            DtpCoreProperties.NacosConfig nacosConfig = dtpCoreProperties.getNacos();
            if (!StringUtils.hasText(nacosConfig.getServerAddr())) {
                log.warn("NacosCoreRefresher initialize failed, serverAddr is empty");
                return;
            }

            Properties properties = buildNacosProperties(nacosConfig);
            configService = NacosFactory.createConfigService(properties);

            // 添加配置监听器
            addConfigListener(nacosConfig);
            
            initialized = true;
            log.info("NacosCoreRefresher initialize success, serverAddr: {}, dataId: {}, group: {}",
                    nacosConfig.getServerAddr(), nacosConfig.getDataId(), nacosConfig.getGroup());
        } catch (Exception e) {
            log.error("NacosCoreRefresher initialize failed", e);
        }
    }

    /**
     * 构建Nacos配置属性
     */
    private Properties buildNacosProperties(DtpCoreProperties.NacosConfig nacosConfig) {
        Properties properties = new Properties();
        properties.setProperty("serverAddr", nacosConfig.getServerAddr());
        
        if (StringUtils.hasText(nacosConfig.getNamespace())) {
            properties.setProperty("namespace", nacosConfig.getNamespace());
        }
        if (StringUtils.hasText(nacosConfig.getUsername())) {
            properties.setProperty("username", nacosConfig.getUsername());
        }
        if (StringUtils.hasText(nacosConfig.getPassword())) {
            properties.setProperty("password", nacosConfig.getPassword());
        }
        if (StringUtils.hasText(nacosConfig.getAccessKey())) {
            properties.setProperty("accessKey", nacosConfig.getAccessKey());
        }
        if (StringUtils.hasText(nacosConfig.getSecretKey())) {
            properties.setProperty("secretKey", nacosConfig.getSecretKey());
        }
        
        properties.setProperty("connectTimeout", String.valueOf(nacosConfig.getConnectTimeout()));
        properties.setProperty("readTimeout", String.valueOf(nacosConfig.getReadTimeout()));
        
        return properties;
    }

    /**
     * 添加配置监听器
     */
    private void addConfigListener(DtpCoreProperties.NacosConfig nacosConfig) throws NacosException {
        configService.addListener(nacosConfig.getDataId(), nacosConfig.getGroup(), new Listener() {
            @Override
            public Executor getExecutor() {
                return null;
            }

            @Override
            public void receiveConfigInfo(String configInfo) {
                log.info("NacosCoreRefresher received config change, dataId: {}, group: {}",
                        nacosConfig.getDataId(), nacosConfig.getGroup());
                
                try {
                    refreshConfig(configInfo, nacosConfig.getType());
                } catch (Exception e) {
                    log.error("NacosCoreRefresher refresh config failed", e);
                }
            }
        });
    }

    /**
     * 刷新配置
     */
    private void refreshConfig(String configContent, String configType) {
        if (!StringUtils.hasText(configContent)) {
            log.warn("NacosCoreRefresher refresh config failed, configContent is empty");
            return;
        }

        try {
            DtpCoreProperties newProperties = parseConfig(configContent, configType);
            if (newProperties != null && newProperties.getExecutors() != null) {
                DtpCoreRegistry.refresh(newProperties);
                log.info("NacosCoreRefresher refresh config success, executors count: {}",
                        newProperties.getExecutors().size());
            }
        } catch (Exception e) {
            log.error("NacosCoreRefresher refresh config error", e);
        }
    }

    /**
     * 解析配置内容
     */
    private DtpCoreProperties parseConfig(String configContent, String configType) {
        try {
            if ("yaml".equalsIgnoreCase(configType) || "yml".equalsIgnoreCase(configType)) {
                return ConfigParseUtil.parseYamlConfig(configContent);
            } else if ("properties".equalsIgnoreCase(configType)) {
                return ConfigParseUtil.parsePropertiesConfig(configContent);
            } else {
                log.warn("NacosCoreRefresher unsupported config type: {}", configType);
                return null;
            }
        } catch (Exception e) {
            log.error("NacosCoreRefresher parse config failed, configType: {}", configType, e);
            return null;
        }
    }

    /**
     * 销毁资源
     */
    public void destroy() {
        if (configService != null) {
            try {
                configService.shutDown();
                log.info("NacosCoreRefresher destroy success");
            } catch (Exception e) {
                log.error("NacosCoreRefresher destroy failed", e);
            }
        }
    }

    /**
     * 检查是否已初始化
     */
    public boolean isInitialized() {
        return initialized;
    }
}
