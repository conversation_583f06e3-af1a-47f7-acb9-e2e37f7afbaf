/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.dynamictp.starter.core.processor;

import lombok.Data;
import org.dromara.dynamictp.starter.core.annotation.DynamicThreadPool;

/**
 * 线程池定义
 * 
 * 封装从注解解析出的线程池配置信息
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
public class ThreadPoolDefinition {

    /**
     * 线程池名称
     */
    private String threadPoolName;

    /**
     * 注解信息
     */
    private DynamicThreadPool annotation;

    /**
     * 来源信息（类名或方法名）
     */
    private String source;

    /**
     * 创建时间
     */
    private long createTime;

    /**
     * 优先级
     */
    private int priority;

    /**
     * 是否启用刷新
     */
    private boolean enableRefresh;

    /**
     * 描述信息
     */
    private String description;

    public ThreadPoolDefinition(DynamicThreadPool annotation, String threadPoolName, String source) {
        this.annotation = annotation;
        this.threadPoolName = threadPoolName;
        this.source = source;
        this.createTime = System.currentTimeMillis();
        this.priority = annotation.priority();
        this.enableRefresh = annotation.enableRefresh();
        this.description = annotation.description();
    }

    /**
     * 转换为配置属性
     */
    public org.dromara.dynamictp.starter.core.properties.DtpCoreProperties.ThreadPoolProperties toProperties() {
        org.dromara.dynamictp.starter.core.properties.DtpCoreProperties.ThreadPoolProperties props = 
            new org.dromara.dynamictp.starter.core.properties.DtpCoreProperties.ThreadPoolProperties();
        
        props.setThreadPoolName(threadPoolName);
        props.setCorePoolSize(annotation.corePoolSize());
        
        int maximumPoolSize = annotation.maximumPoolSize();
        if (maximumPoolSize <= 0) {
            maximumPoolSize = Runtime.getRuntime().availableProcessors();
        }
        props.setMaximumPoolSize(maximumPoolSize);
        
        props.setKeepAliveTime(annotation.keepAliveTime());
        props.setUnit(annotation.timeUnit());
        props.setQueueType(annotation.queueType());
        props.setQueueCapacity(annotation.queueCapacity());
        props.setRejectedHandlerType(annotation.rejectedHandlerType());
        
        String threadNamePrefix = annotation.threadNamePrefix();
        if (threadNamePrefix.isEmpty()) {
            threadNamePrefix = threadPoolName;
        }
        props.setThreadNamePrefix(threadNamePrefix);
        
        props.setAllowCoreThreadTimeOut(annotation.allowCoreThreadTimeOut());
        props.setPreStartAllCoreThreads(annotation.preStartAllCoreThreads());
        props.setFair(annotation.fair());
        
        return props;
    }

    @Override
    public String toString() {
        return "ThreadPoolDefinition{" +
                "threadPoolName='" + threadPoolName + '\'' +
                ", source='" + source + '\'' +
                ", priority=" + priority +
                ", enableRefresh=" + enableRefresh +
                ", description='" + description + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
