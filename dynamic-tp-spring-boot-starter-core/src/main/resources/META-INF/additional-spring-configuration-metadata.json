{"properties": [{"name": "dynamictp.core.enabled", "type": "java.lang.Bo<PERSON>an", "defaultValue": true, "description": "是否启用动态线程池核心功能"}, {"name": "dynamictp.core.enabled-banner", "type": "java.lang.Bo<PERSON>an", "defaultValue": true, "description": "是否打印启动横幅"}, {"name": "dynamictp.core.env", "type": "java.lang.String", "description": "应用环境标识"}, {"name": "dynamictp.core.executors", "type": "java.util.List<org.dromara.dynamictp.starter.core.properties.DtpCoreProperties$ThreadPoolProperties>", "description": "线程池配置列表"}, {"name": "dynamictp.core.nacos.server-addr", "type": "java.lang.String", "defaultValue": "127.0.0.1:8848", "description": "Nacos服务器地址"}, {"name": "dynamictp.core.nacos.namespace", "type": "java.lang.String", "defaultValue": "", "description": "Nacos命名空间"}, {"name": "dynamictp.core.nacos.group", "type": "java.lang.String", "defaultValue": "DEFAULT_GROUP", "description": "Nacos配置组"}, {"name": "dynamictp.core.nacos.data-id", "type": "java.lang.String", "defaultValue": "dynamic-tp-config.yml", "description": "Nacos配置数据ID"}, {"name": "dynamictp.core.nacos.type", "type": "java.lang.String", "defaultValue": "yaml", "description": "Nacos配置类型"}, {"name": "dynamictp.core.nacos.username", "type": "java.lang.String", "description": "Nacos用户名"}, {"name": "dynamictp.core.nacos.password", "type": "java.lang.String", "description": "Nacos密码"}, {"name": "dynamictp.core.nacos.access-key", "type": "java.lang.String", "description": "Nacos访问密钥"}, {"name": "dynamictp.core.nacos.secret-key", "type": "java.lang.String", "description": "Nacos密钥"}, {"name": "dynamictp.core.nacos.connect-timeout", "type": "java.lang.Long", "defaultValue": 3000, "description": "Nacos连接超时时间（毫秒）"}, {"name": "dynamictp.core.nacos.read-timeout", "type": "java.lang.Long", "defaultValue": 5000, "description": "Nacos读取超时时间（毫秒）"}, {"name": "dynamictp.core.annotation.enabled", "type": "java.lang.Bo<PERSON>an", "defaultValue": true, "description": "是否启用注解扫描"}, {"name": "dynamictp.core.annotation.base-packages", "type": "java.lang.String[]", "description": "扫描的基础包路径"}, {"name": "dynamictp.core.annotation.order", "type": "java.lang.Integer", "defaultValue": 0, "description": "扫描优先级"}, {"name": "dynamictp.core.annotation.enable-method-level", "type": "java.lang.Bo<PERSON>an", "defaultValue": true, "description": "是否启用方法级别注解"}, {"name": "dynamictp.core.annotation.enable-class-level", "type": "java.lang.Bo<PERSON>an", "defaultValue": true, "description": "是否启用类级别注解"}]}